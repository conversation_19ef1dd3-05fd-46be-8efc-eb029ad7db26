Stack trace:
Frame         Function      Args
0007FFFF9E90  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9E90, 0007FFFF8D90) msys-2.0.dll+0x1FE8E
0007FFFF9E90  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA168) msys-2.0.dll+0x67F9
0007FFFF9E90  000210046832 (000210286019, 0007FFFF9D48, 0007FFFF9E90, 000000000000) msys-2.0.dll+0x6832
0007FFFF9E90  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9E90  000210068E24 (0007FFFF9EA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA170  00021006A225 (0007FFFF9EA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA84F20000 ntdll.dll
7FFA83C70000 KERNEL32.DLL
7FFA82760000 KERNELBASE.dll
7FFA83E90000 USER32.dll
7FFA82250000 win32u.dll
7FFA84A10000 GDI32.dll
7FFA82570000 gdi32full.dll
7FFA826B0000 msvcp_win.dll
7FFA82100000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA84DD0000 advapi32.dll
7FFA83D50000 msvcrt.dll
7FFA83BC0000 sechost.dll
7FFA84060000 RPCRT4.dll
7FFA816F0000 CRYPTBASE.DLL
7FFA82BF0000 bcryptPrimitives.dll
7FFA84EA0000 IMM32.DLL
