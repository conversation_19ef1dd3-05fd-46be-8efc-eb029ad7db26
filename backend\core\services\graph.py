# backend/core/services/graph.py
import json
import logging
from typing import Dict, List, Optional
import networkx as nx
from ..database import get_neo4j_session

logger = logging.getLogger(__name__)

async def get_knowledge_graph(document_id: str, keywords: Optional[List[str]] = None) -> Dict:
    """
    Generate a knowledge graph for visualization

    Args:
        document_id: ID of the document to generate graph for
        keywords: Optional list of keywords to filter entities

    Returns:
        Dictionary with nodes and links for visualization
    """
    G = nx.DiGraph()

    try:
        with get_neo4j_session() as session:
            # First, let's check if the document exists
            doc_check = session.run(
                "MATCH (d:Document {id: $document_id}) RETURN d.id as id, d.name as name",
                document_id=document_id
            )
            doc_record = doc_check.single()
            if not doc_record:
                logger.warning(f"Document {document_id} not found in database")
                return {"nodes": [], "links": []}

            logger.info(f"Found document: {doc_record['name']} (ID: {doc_record['id']})")

            # Check if there are any entities for this document
            entity_check = session.run(
                "MATCH (e:Entity {document_id: $document_id}) RETURN count(e) as count",
                document_id=document_id
            )
            entity_count = entity_check.single()["count"]
            logger.info(f"Found {entity_count} entities for document {document_id}")

            if entity_count == 0:
                logger.info(f"No entities found for document {document_id}")
                return {"nodes": [], "links": []}

            # Build query based on whether keywords are provided
            if keywords and len(keywords) > 0:
                # Filter by keywords
                keyword_conditions = []
                for keyword in keywords:
                    keyword_conditions.append(f"toLower(e1.name) CONTAINS toLower('{keyword}') OR toLower(e2.name) CONTAINS toLower('{keyword}')")

                keyword_filter = " OR ".join(keyword_conditions)
                query = f"""
                MATCH (e1:Entity)-[r:RELATION]->(e2:Entity)
                WHERE e1.document_id = $document_id AND e2.document_id = $document_id
                AND ({keyword_filter})
                RETURN e1.name as from, type(r) as rel, r.type as rel_type, e2.name as to
                LIMIT 100
                """
            else:
                # No keyword filter
                query = """
                MATCH (e1:Entity)-[r:RELATION]->(e2:Entity)
                WHERE e1.document_id = $document_id AND e2.document_id = $document_id
                RETURN e1.name as from, type(r) as rel, r.type as rel_type, e2.name as to
                LIMIT 100
                """

            logger.info(f"Executing query: {query}")
            # Execute the query
            result = session.run(query, document_id=document_id)

            # Build the graph
            relationship_count = 0
            for record in result:
                G.add_node(record["from"])
                G.add_node(record["to"])
                rel_label = record["rel_type"] if record["rel_type"] else record["rel"]
                G.add_edge(record["from"], record["to"], label=rel_label)
                relationship_count += 1

            logger.info(f"Found {relationship_count} relationships for document {document_id}")

    except Exception as e:
        logger.error(f"Error generating knowledge graph: {str(e)}")
        return {"nodes": [], "links": []}

    if not G.nodes():
        logger.info(f"No relationships found for document {document_id}")
        return {"nodes": [], "links": []}

    # Convert NetworkX graph to D3.js format
    nodes = [{"id": node, "name": node, "group": 1} for node in G.nodes()]
    links = [{"source": u, "target": v, "label": G[u][v]["label"]} for u, v in G.edges()]

    logger.info(f"Returning graph with {len(nodes)} nodes and {len(links)} links")
    return {
        "nodes": nodes,
        "links": links
    }

async def execute_cypher_query(query: str) -> Dict:
    """
    Execute a Cypher query and return results in a format suitable for visualization

    Args:
        query: Cypher query to execute

    Returns:
        Dictionary with query results
    """
    try:
        with get_neo4j_session() as session:
            result = session.run(query)
            records = [dict(record) for record in result]

            # Check if results can be visualized as a graph
            if records and all(key in records[0] for key in ["from_entity", "relationship", "to_entity"]):
                # Build a graph from the results
                G = nx.DiGraph()
                for record in records:
                    G.add_node(record["from_entity"])
                    G.add_node(record["to_entity"])
                    G.add_edge(record["from_entity"], record["to_entity"], label=record["relationship"])

                # Convert to D3.js format
                nodes = [{"id": node, "name": node, "group": 1} for node in G.nodes()]
                links = [{"source": u, "target": v, "label": G[u][v]["label"]} for u, v in G.edges()]

                return {
                    "type": "graph",
                    "data": {
                        "nodes": nodes,
                        "links": links
                    }
                }
            else:
                # Return as tabular data
                return {
                    "type": "table",
                    "data": records
                }

    except Exception as e:
        logger.error(f"Error executing Cypher query: {str(e)}")
        return {
            "type": "error",
            "message": str(e)
        }