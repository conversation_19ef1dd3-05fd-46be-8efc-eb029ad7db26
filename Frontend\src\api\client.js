// src/api/client.js
import axios from "axios";
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add a response interceptor to handle errors
apiClient.interceptors.response.use(
  (response) => {
    // Any status code that lie within the range of 2xx cause this function to trigger
    console.log("API Response:", response.config.url, response.data);
    return response;
  },
  (error) => {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    console.error("API Error:", error.config?.url, error.message);

    // Return a default response for development
    if (error.config?.url.includes("/documents")) {
      console.log("Returning empty documents array for document endpoint");
      // Return empty documents array for document endpoints
      return {
        data: {
          documents: [],
        },
      };
    }

    // For other endpoints, reject with error
    return Promise.reject(error);
  }
);

export default apiClient;
