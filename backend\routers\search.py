# backend/routers/search.py
from fastapi import APIRouter, HTTPException
from core.services.search import (
    semantic_search,
    hybrid_search,
    keyword_search
)
from core.models.schemas import (
    SearchRequest,
    SearchResponse
)

router = APIRouter()

@router.post("/semantic", response_model=SearchResponse)
async def search_semantically(request: SearchRequest):
    try:
        return await semantic_search(
            request.document_id,
            request.query,
            request.limit
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/hybrid", response_model=SearchResponse)
async def search_hybrid(request: SearchRequest):
    try:
        return await hybrid_search(
            request.document_id,
            request.query,
            request.limit
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/keyword", response_model=SearchResponse)
async def search_by_keyword(request: SearchRequest):
    try:
        return await keyword_search(
            request.document_id,
            request.query,
            request.limit
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))