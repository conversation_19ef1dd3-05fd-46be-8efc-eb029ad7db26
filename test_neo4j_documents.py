from neo4j import GraphDatabase
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv("backend/.env")

NEO4J_URI = os.getenv("NEO4J_URI")
NEO4J_USER = os.getenv("NEO4J_USER")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD")

print(f"Attempting to connect to Neo4j at: {NEO4J_URI}")
print(f"With user: {NEO4J_USER}")

try:
    # Initialize Neo4j driver
    driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
    
    # Test connection
    with driver.session() as session:
        print("Connection successful! Testing document listing...")
        
        # Try to list documents
        result = session.run(
            """
            MATCH (d:Document)
            RETURN d.id as id, d.name as name, d.type as type,
                   d.text_preview as preview, d.chunk_count as chunk_count,
                   d.created_at as created_at
            ORDER BY d.created_at DESC
            """
        )
        
        documents = [dict(record) for record in result]
        
        if documents:
            print(f"Found {len(documents)} documents:")
            for doc in documents:
                print(f"- {doc.get('name', 'Unknown')} (ID: {doc.get('id', 'Unknown')})")
        else:
            print("No documents found in the database.")
    
    # Close the driver
    driver.close()
    print("Connection closed.")
    
except Exception as e:
    print(f"Error: {str(e)}")
