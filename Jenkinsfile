pipeline {
    agent any
    options {
        skipDefaultCheckout(true)
        timestamps()
    }
    parameters {
        string(name: 'BRANCH', defaultValue: 'saurabh', description: 'Git branch to build')
    }
    environment {
        PYTHONUNBUFFERED = '1'
        BACKEND_PORT = '8077'
    }

    stages {
        stage('Verify Repository') {
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: '8f94f14d-2ced-4ac1-827d-e69aad71f1fc', usernameVariable: 'GIT_USER', passwordVariable: 'GIT_PASS')]) {
                        if (fileExists("${env.WORKSPACE}/.git")) {
                            echo "Git repo found, pulling latest changes on branch ${params.BRANCH}..."
                            sh """
                                cd ${env.WORKSPACE}
                                git remote set-url origin https://${GIT_USER}:${GIT_PASS}@github.com/64SquaresApexLLP/KnowledgeWeaver.git
                                git fetch origin
                                git checkout ${params.BRANCH}
                                git reset --hard origin/${params.BRANCH}
                            """
                        } else {
                            echo "Git repo not found, cloning fresh branch ${params.BRANCH}..."
                            sh """
                                git clone -b ${params.BRANCH} https://${GIT_USER}:${GIT_PASS}@github.com/64SquaresApexLLP/KnowledgeWeaver.git ${env.WORKSPACE}
                            """
                        }
                    }
                }
            }
        }

        stage('Install Backend Dependencies') {
            steps {
                sh """
                    python3 -m venv ${env.WORKSPACE}/venv
                    ${env.WORKSPACE}/venv/bin/pip install --upgrade pip
                    ${env.WORKSPACE}/venv/bin/pip install -r ${env.WORKSPACE}/backend/requirements.txt
                """
            }
        }

        stage('Restart Backend') {
            steps {
                script {
                    echo "Killing any process using port ${BACKEND_PORT}..."
                    sh "fuser -k ${BACKEND_PORT}/tcp || true"

                    echo "Restarting backend with PM2..."

                    sh """
                        cd ${env.WORKSPACE}/backend
                        export \$(grep -v '^#' .env | xargs)
                        pm2 delete  backend_knowledgeweaver || true
                        pm2 start main.py --name backend_knowledgeweaver --interpreter ${env.WORKSPACE}/venv/bin/python
                    """
                }
            }
        }

        stage('Install Frontend Dependencies') {
            steps {
                sh """
                    cd ${env.WORKSPACE}/Frontend
                    npm install
                """
            }
        }

        stage('Build Frontend') {
            steps {
                sh """
                    cd ${env.WORKSPACE}/Frontend
                    npm run build
                """
            }
        }

        stage('Deploy Frontend') {
    steps {
        script {
            echo "Deploying frontend with dist folder to /mnt/frontend..."
            sh """
                sudo rm -rf /mnt/frontend/dist
                sudo cp -r ${env.WORKSPACE}/Frontend/dist /mnt/frontend/
            """
        }
    }
}

    }

    post {
        always {
            archiveArtifacts artifacts: '**/*.log', allowEmptyArchive: true
        }
        failure {
            mail to: '<EMAIL>', subject: 'Jenkins Build Failed', body: 'Check Jenkins for details.'
        }
    }
}
