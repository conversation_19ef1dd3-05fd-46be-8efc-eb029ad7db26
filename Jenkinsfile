pipeline {
    agent any
    options {
        skipDefaultCheckout(true)
        timestamps()
    }
    parameters {
        string(name: 'BRANCH', defaultValue: 'saurabh', description: 'Git branch to build')
    }
    environment {
        PYTHONUNBUFFERED = '1'
        BACKEND_PORT = '8077'
    }

    stages {
        stage('Verify Repository') {
            steps {
                script {
                    if (fileExists("${env.WORKSPACE}/.git")) {
                        sh "git status"
                        sh "git branch --show-current"
                    } else {
                        error "No git repository found in ${env.WORKSPACE}. Please clone manually before running this pipeline."
                    }
                }
            }
        }

   stage('Install Backend Dependencies') {
    steps {
        sh '''#!/bin/bash
        python3 -m venv venv
        source venv/bin/activate
        pip install -r backend/requirements.txt
        '''
    }
}


        stage('Restart Backend') {
            steps {
                script {
                    echo "Killing any process using port ${BACKEND_PORT}..."
                    sh "fuser -k ${BACKEND_PORT}/tcp || true"

                    echo "Restarting backend with PM2..."
                    sh """
                        cd "${env.WORKSPACE}/backend"
                        export \$(grep -v '^#' .env | xargs)
                        pm2 delete backend || true
                        pm2 start main.py --name backend --interpreter python
                    """
                }
            }
        }

        stage('Install Frontend Dependencies') {
            steps {
                sh """
                    cd "${WORKSPACE}/Frontend"
                    npm install
                """
            }
        }

        stage('Build Frontend') {
            steps {
                sh """
                    cd "${WORKSPACE}/Frontend"
                    npm run build
                """
            }
        }

        stage('Deploy Frontend') {
            steps {
                script {
                    echo "Deploying frontend to /var/www/frontend..."
                    sh "sudo rm -rf /var/www/frontend && sudo cp -r \"${WORKSPACE}/Frontend/dist\" /var/www/frontend"
                }
            }
        }
    }

    post {
        always {
            archiveArtifacts artifacts: '**/*.log', allowEmptyArchive: true
        }
        failure {
            mail to: '<EMAIL>', subject: 'Jenkins Build Failed', body: 'Check Jenkins for details.'
        }
    }
}
