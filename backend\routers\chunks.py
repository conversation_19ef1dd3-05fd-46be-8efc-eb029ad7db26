from fastapi import APIRouter, HTTPException
from typing import List
from core.services.chunks import (
    get_chunks_by_document,
    get_chunk_by_id,
    search_chunks
)
from core.models.schemas import ChunkResponse

router = APIRouter(prefix="/chunks", tags=["Chunks"])

@router.get("/document/{document_id}", response_model=List[ChunkResponse])
async def get_document_chunks(document_id: str, limit: int = 100):
    try:
        return await get_chunks_by_document(document_id, limit)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{chunk_id}", response_model=ChunkResponse)
async def get_chunk(chunk_id: str):
    chunk = await get_chunk_by_id(chunk_id)
    if not chunk:
        raise HTTPException(status_code=404, detail="Chunk not found")
    return chunk

@router.get("/search/{document_id}", response_model=List[ChunkResponse])
async def search_chunks_in_document(document_id: str, q: str, limit: int = 10):
    try:
        return await search_chunks(document_id, q, limit)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))