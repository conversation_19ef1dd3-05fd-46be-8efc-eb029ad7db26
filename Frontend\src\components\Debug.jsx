// src/components/Debug.jsx
import React, { useState } from 'react';

const Debug = ({ documents }) => {
  const [showDebug, setShowDebug] = useState(false);

  if (!showDebug) {
    return (
      <button 
        className="fixed bottom-4 right-4 bg-gray-800 text-white p-2 rounded-lg z-50"
        onClick={() => setShowDebug(true)}
      >
        Show Debug
      </button>
    );
  }

  return (
    <div className="fixed bottom-0 right-0 bg-gray-900 text-white p-4 rounded-lg shadow-lg z-50 max-w-lg max-h-96 overflow-auto">
      <div className="flex justify-between mb-2">
        <h3 className="text-lg font-bold">Debug Info</h3>
        <button 
          className="text-gray-400 hover:text-white"
          onClick={() => setShowDebug(false)}
        >
          Close
        </button>
      </div>
      
      <div className="mb-4">
        <h4 className="text-md font-semibold mb-1">Documents ({documents?.length || 0})</h4>
        {documents && documents.length > 0 ? (
          <pre className="text-xs bg-gray-800 p-2 rounded overflow-auto">
            {JSON.stringify(documents.slice(0, 3), null, 2)}
            {documents.length > 3 && '... (more documents)'}
          </pre>
        ) : (
          <p className="text-sm text-gray-400">No documents available</p>
        )}
      </div>
    </div>
  );
};

export default Debug;
