from typing import List, Dict, Optional
from neo4j import Record
from ..database import get_neo4j_session
from ..models.schemas import ChunkResponse

async def get_chunks_by_document(document_id: str, limit: int = 100) -> List[ChunkResponse]:
    """
    Retrieve all chunks for a specific document
    """
    with get_neo4j_session() as session:
        result = session.run(
            """
            MATCH (d:Document {id: $doc_id})-[:HAS_CHUNK]->(c:Chunk)
            RETURN c.id as id, c.text as text, c.index as index, d.id as document_id
            ORDER BY c.index
            LIMIT $limit
            """,
            doc_id=document_id,
            limit=limit
        )
        return [
            ChunkResponse(
                id=record["id"],
                text=record["text"],
                index=record["index"],
                document_id=record["document_id"]
            )
            for record in result
        ]

async def get_chunk_by_id(chunk_id: str) -> Optional[ChunkResponse]:
    """
    Get a specific chunk by its ID
    """
    with get_neo4j_session() as session:
        result = session.run(
            """
            MATCH (c:Chunk {id: $chunk_id})
            OPTIONAL MATCH (d:Document)-[:HAS_CHUNK]->(c)
            RETURN c.id as id, c.text as text, c.index as index, d.id as document_id
            """,
            chunk_id=chunk_id
        )
        record = result.single()
        if not record:
            return None

        return ChunkResponse(
            id=record["id"],
            text=record["text"],
            index=record["index"],
            document_id=record["document_id"]
        )

async def search_chunks(document_id: str, query: str, limit: int = 10) -> List[ChunkResponse]:
    """
    Search chunks containing specific text
    """
    with get_neo4j_session() as session:
        result = session.run(
            """
            MATCH (d:Document {id: $doc_id})-[:HAS_CHUNK]->(c:Chunk)
            WHERE toLower(c.text) CONTAINS toLower($query)
            RETURN c.id as id, c.text as text, c.index as index
            ORDER BY c.index
            LIMIT $limit
            """,
            doc_id=document_id,
            query=query,
            limit=limit
        )
        return [ChunkResponse(**dict(record)) for record in result]