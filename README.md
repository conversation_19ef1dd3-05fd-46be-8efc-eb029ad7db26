# Document Knowledge Graph API

A FastAPI-based backend for document processing, knowledge graph extraction, and question answering.

## Features

- Document upload and processing (PDF, DOCX, images)
- Text extraction and chunking
- Entity and relationship extraction using OpenAI
- Knowledge graph visualization
- Semantic search
- Question answering using RAG (Retrieval Augmented Generation)

## Requirements

- Python 3.8+
- Neo4j database
- OpenAI API key

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/KnowledgeWeaver.git
cd KnowledgeWeaver
```

2. Create a virtual environment and install dependencies:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. Create a `.env` file in the root directory with the following variables:
```
NEO4J_URI=neo4j+s://your-neo4j-instance.databases.neo4j.io
NEO4J_USER=your-username
NEO4J_PASSWORD=your-password
OPENAI_API_KEY=your-openai-api-key
```

## Running the Application

### Backend (FastAPI)

Run the FastAPI application:
```bash
cd backend
uvicorn main:app --reload
```

The API will be available at http://localhost:8000, and the API documentation at http://localhost:8000/docs.

### Frontend (React)

The frontend is a React application that can be run separately. Instructions for setting up the React frontend will be provided in a separate document.

## API Endpoints

- `/api/documents` - Document upload and management
- `/api/chat` - Question answering
- `/api/graph` - Knowledge graph visualization
- `/api/entities` - Entity management
- `/api/chunks` - Text chunk management
- `/api/search` - Search functionality

## License

[MIT License](LICENSE)
