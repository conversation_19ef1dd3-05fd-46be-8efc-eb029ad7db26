import { Send, ChevronLeft, ChevronRight, X, Menu } from "lucide-react";
const MiddleChatSection = ({
  leftSidebarVisible,
  chatHistory,
  messageEndRef,
  setInputMessage,
  inputMessage,
  handleSendMessage,
  toggleLeftSidebar
}) => {
  return (
    <>
      <div className="w-64 border-r border-gray-800 flex-1 flex flex-col relative">
        {/* Left sidebar toggle button (when expanded) */}
        {!leftSidebarVisible && (
          <button
            className="absolute left-4 top-4 p-2 rounded hover:bg-gray-700"
            onClick={toggleLeftSidebar}
          >
            <Menu size={20} />
          </button>
        )}

        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {chatHistory.map((message, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg max-w-lg ${
                message.role === "assistant"
                  ? "bg-gray-800"
                  : "bg-purple-600 ml-auto"
              }`}
            >
              <p>{message.content}</p>
            </div>
          ))}
          <div ref={messageEndRef} />
        </div>

        {/* Input Area */}
        <div className="p-4 border-t border-gray-800">
          <div className="flex items-center bg-gray-800 rounded-lg p-2">
            <input
              type="text"
              placeholder="Ask a question about your documents..."
              className="flex-1 bg-transparent outline-none text-white"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
            />
            <button
              className="p-2 bg-purple-600 rounded-full"
              onClick={handleSendMessage}
            >
              <Send size={16} />
            </button>
          </div>
        </div>
      </div>
    </>
  );
};
export default MiddleChatSection;
