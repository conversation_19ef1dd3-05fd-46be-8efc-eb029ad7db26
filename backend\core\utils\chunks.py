from typing import List, Dict, Optional
from neo4j import Record
from ..database import get_neo4j_session
from ..models.schemas import ChunkResponse

async def get_chunks_by_document(document_id: str, limit: int = 100) -> List[Dict]:
    """
    Retrieve all chunks for a specific document
    """
    with get_neo4j_session() as session:
        result = session.run(
            """
            MATCH (d:Document {id: $doc_id})-[:HAS_CHUNK]->(c:Chunk)
            RETURN c.id as id, c.text as text, c.index as index
            ORDER BY c.index
            LIMIT $limit
            """,
            doc_id=document_id,
            limit=limit
        )
        return [dict(record) for record in result]

async def get_chunk_by_id(chunk_id: str) -> Optional[Dict]:
    """
    Get a specific chunk by its ID
    """
    with get_neo4j_session() as session:
        result = session.run(
            """
            MATCH (c:Chunk {id: $chunk_id})
            RETURN c.id as id, c.text as text, c.index as index
            """,
            chunk_id=chunk_id
        )
        record = result.single()
        return dict(record) if record else None

async def search_chunks(document_id: str, query: str, limit: int = 10) -> List[ChunkResponse]:
    """
    Search chunks containing specific text
    """
    with get_neo4j_session() as session:
        result = session.run(
            """
            MATCH (d:Document {id: $doc_id})-[:HAS_CHUNK]->(c:Chunk)
            WHERE toLower(c.text) CONTAINS toLower($query)
            RETURN c.id as id, c.text as text, c.index as index
            ORDER BY c.index
            LIMIT $limit
            """,
            doc_id=document_id,
            query=query,
            limit=limit
        )
        return [ChunkResponse(**dict(record)) for record in result]