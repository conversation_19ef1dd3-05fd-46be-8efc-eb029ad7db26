// import { Send, ChevronLeft, ChevronRight, X, <PERSON>u } from "lucide-react";
// const GraphVisualization = ({toggleLeftSidebar,toggleRightPanel,rightPanelVisible}) => {
//   return (
//     <>
//       {" "}
//       <div className="flex-1 p-4 flex flex-col">
//         {/* Graph Controls */}
//         <div className=" flex gap-2 mb-4">
//           <button className="px-3 py-1 bg-gray-700 text-sm rounded">
//             Zoom In
//           </button>
//           <button className="px-3 py-1 bg-gray-700 text-sm rounded">
//             Zoom Out
//           </button>
//           <button className="px-3 py-1 bg-gray-700 text-sm rounded">
//             Reset
//           </button>
//           <div className="ml-auto flex gap-2">
//             <button className="px-3 py-1 bg-gray-700 text-sm rounded">
//               Filter
//             </button>
//             <button className="px-3 py-1 bg-gray-700 text-sm rounded">
//               Export
//             </button>
//             {/* Right panel toggle button */}
//             <button
//               className="px-3 py-1 bg-gray-700 text-sm rounded flex items-center gap-1"
//               onClick={toggleRightPanel}
//             >
//               {rightPanelVisible ? (
//                 <>
//                   Hide Details
//                   <ChevronRight size={14} />
//                 </>
//               ) : (
//                 <>
//                   Show Details
//                   <ChevronLeft size={14} />
//                 </>
//               )}
//             </button>
//           </div>
//         </div>

//         {/* Graph Visualization */}
//         <div className="flex-1 bg-gray-800 rounded-lg mb-4 relative overflow-hidden">
//           {/* Simple graph visualization mockup */}
//           <svg className="w-full h-full" viewBox="0 0 400 400">
//             {/* Company Y node */}
//             <circle cx="200" cy="100" r="20" fill="#9333ea" />
//             <text
//               x="200"
//               y="100"
//               textAnchor="middle"
//               fill="white"
//               fontSize="10"
//             >
//               Company Y
//             </text>

//             {/* Company X node */}
//             <circle cx="120" cy="150" r="20" fill="#10b981" />
//             <text
//               x="120"
//               y="150"
//               textAnchor="middle"
//               fill="white"
//               fontSize="10"
//             >
//               Company X
//             </text>

//             {/* CFO node */}
//             <circle cx="280" cy="150" r="15" fill="#f59e0b" />
//             <text
//               x="280"
//               y="150"
//               textAnchor="middle"
//               fill="white"
//               fontSize="10"
//             >
//               CFO
//             </text>

//             {/* Subsidiary node */}
//             <circle cx="150" cy="220" r="15" fill="#3b82f6" />
//             <text
//               x="150"
//               y="220"
//               textAnchor="middle"
//               fill="white"
//               fontSize="10"
//             >
//               Subsidiary
//             </text>

//             {/* Project node */}
//             <circle cx="250" cy="200" r="15" fill="#ef4444" />
//             <text
//               x="250"
//               y="200"
//               textAnchor="middle"
//               fill="white"
//               fontSize="10"
//             >
//               Project
//             </text>

//             {/* Connections */}
//             <path
//               d="M190 110 L130 140"
//               stroke="#9333ea"
//               strokeWidth="2"
//               strokeDasharray="5,5"
//               opacity="0.6"
//             />
//             <path
//               d="M210 110 L270 140"
//               stroke="#9333ea"
//               strokeWidth="2"
//               strokeDasharray="5,5"
//               opacity="0.6"
//             />
//             <path
//               d="M130 160 L150 205"
//               stroke="#10b981"
//               strokeWidth="2"
//               strokeDasharray="5,5"
//               opacity="0.6"
//             />
//             <path
//               d="M280 165 L255 190"
//               stroke="#f59e0b"
//               strokeWidth="2"
//               strokeDasharray="5,5"
//               opacity="0.6"
//             />
//             <path
//               d="M165 215 L235 200"
//               stroke="#3b82f6"
//               strokeWidth="2"
//               strokeDasharray="5,5"
//               opacity="0.6"
//             />
//             <path
//               d="M200 120 L250 180"
//               stroke="#9333ea"
//               strokeWidth="2"
//               strokeDasharray="5,5"
//               opacity="0.6"
//             />
//           </svg>

//           {/* Knowledge Base Card */}
//           <div className="absolute bottom-4 right-4 w-48 h-24 bg-gray-900 rounded-lg flex items-center justify-center">
//             <span className="text-purple-400 font-bold tracking-wider">
//               KNOWLEDGE BASE
//             </span>
//           </div>
//         </div>
//       </div>
//     </>
//   );
// };
// export default GraphVisualization;




// src/components/GraphVisualization.jsx
import { Send, ChevronLeft, ChevronRight, X, Menu } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { getKnowledgeGraph, filterKnowledgeGraph } from "../api/services";
import * as d3 from "d3";

const GraphVisualization = ({
  toggleLeftSidebar,
  toggleRightPanel,
  rightPanelVisible,
  selectedDocument
}) => {
  const [graphData, setGraphData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [keywords, setKeywords] = useState("");
  const svgRef = useRef(null);

  useEffect(() => {
    if (selectedDocument) {
      fetchGraph();
      console.log("Selected document:", selectedDocument);
    }
  }, [selectedDocument]);

  const fetchGraph = async () => {
    if (!selectedDocument) return;
    
    try {
      setLoading(true);
      const response = await getKnowledgeGraph(selectedDocument.document_id);
      setGraphData(response.data);
      console.log("Graph data:", response.data);
      setError(null);
    } catch (err) {
      console.error("Error fetching graph:", err);
      setError("Failed to fetch graph data");
    } finally {
      setLoading(false);
    }
  };

  const handleFilterGraph = async () => {
    if (!selectedDocument) return;
    if (!keywords.trim()) {
      fetchGraph();
      return;
    }

    try {
      setLoading(true);
      const keywordArray = keywords.split(',').map(k => k.trim()).filter(k => k);
      const response = await filterKnowledgeGraph(selectedDocument.document_id, keywordArray);
      setGraphData(response.data);
      setError(null);
    } catch (err) {
      console.error("Error filtering graph:", err);
      setError("Failed to filter graph");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!graphData || !graphData.nodes || !graphData.links || graphData.nodes.length === 0) return;

    // Clear previous graph
    d3.select(svgRef.current).selectAll("*").remove();

    const svg = d3.select(svgRef.current);
    const width = svg.node().getBoundingClientRect().width;
    const height = svg.node().getBoundingClientRect().height;

    // Create a simulation with forces
    const simulation = d3.forceSimulation(graphData.nodes)
      .force("link", d3.forceLink(graphData.links).id(d => d.id).distance(100))
      .force("charge", d3.forceManyBody().strength(-300))
      .force("center", d3.forceCenter(width / 2, height / 2));

    // Create links
    const link = svg.append("g")
      .selectAll("line")
      .data(graphData.links)
      .join("line")
      .attr("stroke", "#999")
      .attr("stroke-opacity", 0.6)
      .attr("stroke-width", 1.5);

    // Create link labels
    const linkLabels = svg.append("g")
      .selectAll("text")
      .data(graphData.links)
      .join("text")
      .text(d => d.label)
      .attr("font-size", 10)
      .attr("text-anchor", "middle")
      .attr("fill", "white")
      .attr("dy", -5);

    // Create nodes
    const node = svg.append("g")
      .selectAll("circle")
      .data(graphData.nodes)
      .join("circle")
      .attr("r", 10)
      .attr("fill", "#9333ea")
      .call(drag(simulation));

    // Create node labels
    const nodeLabels = svg.append("g")
      .selectAll("text")
      .data(graphData.nodes)
      .join("text")
      .text(d => d.name)
      .attr("font-size", 12)
      .attr("text-anchor", "middle")
      .attr("fill", "white")
      .attr("dy", 25);

    // Update positions on each tick
    simulation.on("tick", () => {
      link
        .attr("x1", d => d.source.x)
        .attr("y1", d => d.source.y)
        .attr("x2", d => d.target.x)
        .attr("y2", d => d.target.y);

      linkLabels
        .attr("x", d => (d.source.x + d.target.x) / 2)
        .attr("y", d => (d.source.y + d.target.y) / 2);

      node
        .attr("cx", d => d.x)
        .attr("cy", d => d.y);

      nodeLabels
        .attr("x", d => d.x)
        .attr("y", d => d.y);
    });

    // Drag functionality
    function drag(simulation) {
      function dragstarted(event) {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        event.subject.fx = event.subject.x;
        event.subject.fy = event.subject.y;
      }
      
      function dragged(event) {
        event.subject.fx = event.x;
        event.subject.fy = event.y;
      }
      
      function dragended(event) {
        if (!event.active) simulation.alphaTarget(0);
        event.subject.fx = null;
        event.subject.fy = null;
      }
      
      return d3.drag()
        .on("start", dragstarted)
        .on("drag", dragged)
        .on("end", dragended);
    }

    return () => {
      simulation.stop();
    };
  }, [graphData]);

  return (
    <>
      <div className="flex-1 p-4 flex flex-col">
        {/* Graph Controls */}
        <div className="flex gap-2 mb-4">
          <input
            type="text"
            placeholder="Filter by keywords (comma separated)"
            className="flex-1 px-3 py-1 bg-gray-700 text-sm rounded"
            value={keywords}
            onChange={(e) => setKeywords(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && handleFilterGraph()}
          />
          <button 
            className="px-3 py-1 bg-purple-600 text-sm rounded"
            onClick={handleFilterGraph}
            disabled={loading}
          >
            {loading ? "Filtering..." : "Filter"}
          </button>
          <button 
            className="px-3 py-1 bg-gray-700 text-sm rounded"
            onClick={fetchGraph}
            disabled={loading}
          >
            Reset
          </button>
          <div className="ml-auto flex gap-2">
            <button 
              className="px-3 py-1 bg-gray-700 text-sm rounded flex items-center gap-1"
              onClick={toggleRightPanel}
            >
              {rightPanelVisible ? (
                <>
                  Hide Details
                  <ChevronRight size={14} />
                </>
              ) : (
                <>
                  Show Details
                  <ChevronLeft size={14} />
                </>
              )}
            </button>
          </div>
        </div>

        {/* Graph Visualization */}
        <div className="flex-1 bg-gray-800 rounded-lg mb-4 relative overflow-hidden">
          {loading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-800 bg-opacity-75">
              <div className="text-white">Loading graph...</div>
            </div>
          )}
          
          {error && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-800 bg-opacity-75">
              <div className="text-red-500">{error}</div>
            </div>
          )}
          
          {!selectedDocument && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-gray-400">Select a document to view its knowledge graph</div>
            </div>
          )}
          
          {selectedDocument && !graphData && !loading && !error && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-gray-400">No graph data available for this document</div>
            </div>
          )}
          
          <svg ref={svgRef} className="w-full h-full"></svg>
        </div>
      </div>
    </>
  );
};
export default GraphVisualization;