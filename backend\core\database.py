from neo4j import GraphDatabase
from .config import settings
import logging

logger = logging.getLogger(__name__)

try:
    driver = GraphDatabase.driver(
        settings.NEO4J_URI,
        auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
    )
    # Test the connection
    with driver.session() as session:
        session.run("RETURN 1")
    logger.info("Successfully connected to Neo4j database")
except Exception as e:
    logger.error(f"Failed to connect to Neo4j: {str(e)}")
    driver = None

def get_neo4j_session():
    if driver is None:
        raise ConnectionError("Neo4j database is not available")
    return driver.session()