from typing import List, Tuple, Optional
import re
from langchain.schema import HumanMessage
from langchain_openai import ChatOpenAI  # Updated import
from ..config import settings
from ..database import get_neo4j_session
import logging

logger = logging.getLogger(__name__)

# Initialize OpenAI client
llm = ChatOpenAI(openai_api_key=settings.OPENAI_API_KEY)

async def extract_entities_with_openai(text: str, doc_id: Optional[str] = None) -> str:
    """
    Extract entities and relationships from text using OpenAI

    Args:
        text: The text to extract entities from
        doc_id: Optional ID of the document (used for logging)
    """
    # Log processing information
    if doc_id:
        logger.info(f"Extracting entities for document: {doc_id}")

    # Limit text to avoid token limits
    max_text_length = 4000
    if len(text) > max_text_length:
        logger.info(f"Text too long ({len(text)} chars), truncating to {max_text_length} chars")
        text = text[:max_text_length]

    prompt = f"""
    Extract the most important entities and relationships from the following content as triples (subject - relation - object).
    Focus on identifying key entities and their meaningful relationships.
    Be specific and detailed in the relationships.
    Aim to extract 15-20 high-quality triples that represent the main concepts in the text.
    Return ONLY a list of triples, one per line, in the format: subject - relation - object

    Content:
    {text}
    """
    try:
        response = llm([HumanMessage(content=prompt)])
        return response.content
    except Exception as e:
        logger.error(f"Error extracting entities: {str(e)}")
        return ""

def parse_triples(triples_text: str) -> List[Tuple[str, str, str]]:
    """Parse triples from various formats that might be returned by the LLM"""
    # Try to parse as list items (1. subject - relation - object)
    numbered_pattern = r'\d+\.\s*(.*?)\s*-\s*(.*?)\s*-\s*(.*?)(?:\n|$)'
    matches = re.findall(numbered_pattern, triples_text)
    if matches:
        return [(s.strip(), r.strip(), o.strip()) for s, r, o in matches]

    # Try to parse as plain triples (subject - relation - object)
    plain_pattern = r'(.*?)\s*-\s*(.*?)\s*-\s*(.*?)(?:\n|$)'
    matches = re.findall(plain_pattern, triples_text)
    if matches:
        return [(s.strip(), r.strip(), o.strip()) for s, r, o in matches]

    # Try to parse as bullet points (• subject - relation - object)
    bullet_pattern = r'[•\*\-]\s*(.*?)\s*-\s*(.*?)\s*-\s*(.*?)(?:\n|$)'
    matches = re.findall(bullet_pattern, triples_text)
    if matches:
        return [(s.strip(), r.strip(), o.strip()) for s, r, o in matches]

    # No matches found
    logger.warning("Could not parse any triples from the text")
    return []

async def populate_neo4j(triples_text: str, document_id: str, chunk_id: Optional[str] = None, chunk_text: Optional[str] = None) -> bool:
    """
    Populate Neo4j with triples extracted from text.
    If chunk_id is provided, associate the triples with that chunk.
    Otherwise, associate them with the document directly.
    """
    triples = parse_triples(triples_text)
    if not triples:
        logger.warning("No valid triples found in the extracted text.")
        return False

    try:
        with get_neo4j_session() as session:
            # If this is a chunk, create or update the Chunk node
            if chunk_id and chunk_text:
                # Create a Chunk node
                session.run(
                    """
                    MERGE (c:Chunk {id: $chunk_id, document_id: $document_id})
                    SET c.text = $text
                    """,
                    chunk_id=chunk_id, document_id=document_id, text=chunk_text[:1000]  # Store a preview of the text
                )

            # Process each triple
            for subject, relation, obj in triples:
                # Create the basic triple structure
                query = """
                MERGE (s:Entity {name: $subject, document_id: $document_id})
                MERGE (o:Entity {name: $object, document_id: $document_id})
                MERGE (s)-[r:RELATION {type: $relation, document_id: $document_id}]->(o)
                """

                # If this is from a chunk, connect entities to the chunk
                if chunk_id:
                    query += """
                    WITH s, o
                    MATCH (c:Chunk {id: $chunk_id})
                    MERGE (s)-[:APPEARS_IN]->(c)
                    MERGE (o)-[:APPEARS_IN]->(c)
                    """
                else:
                    # If no chunk_id, connect entities directly to the document
                    query += """
                    WITH s, o
                    MATCH (d:Document {id: $document_id})
                    MERGE (s)-[:BELONGS_TO]->(d)
                    MERGE (o)-[:BELONGS_TO]->(d)
                    """

                session.run(
                    query,
                    subject=subject, object=obj, relation=relation,
                    document_id=document_id, chunk_id=chunk_id
                )
        return True
    except Exception as e:
        logger.error(f"Error populating Neo4j: {str(e)}")
        return False

async def extract_keywords_from_question(question: str) -> List[str]:
    """Extract key entities and concepts from the question"""
    prompt = f"""
    Extract the key entities, concepts, and search terms from this question.
    Return ONLY a comma-separated list of important terms (nouns, proper nouns, key concepts).

    Question: {question}
    """

    try:
        response = llm([HumanMessage(content=prompt)])
        keywords = [kw.strip() for kw in response.content.split(',')]
        return [kw for kw in keywords if kw]  # Filter out empty strings
    except Exception as e:
        logger.error(f"Error extracting keywords: {str(e)}")
        # Fallback to simple word extraction
        return [w for w in re.findall(r'\b\w+\b', question) if len(w) > 3]
