# backend/routers/history.py
from fastapi import APIRouter, HTTPException
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime

router = APIRouter()

class HistoryEntry(BaseModel):
    id: str
    document_id: str
    question: str
    answer: str
    timestamp: datetime

class HistoryResponse(BaseModel):
    entries: List[HistoryEntry]

@router.get("/{document_id}", response_model=HistoryResponse)
async def get_document_history(document_id: str, limit: int = 10):
    """Get conversation history for a document"""
    try:
        # In a real implementation, you would retrieve the history from a database
        # For now, we return an empty list
        return {"entries": []}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{history_id}")
async def delete_history_entry(history_id: str):
    """Delete a history entry"""
    try:
        # In a real implementation, you would delete the entry from a database
        # For now, we just return a success message
        return {"message": f"History entry {history_id} deleted"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/document/{document_id}")
async def clear_document_history(document_id: str):
    """Clear all history for a document"""
    try:
        # In a real implementation, you would delete all entries for the document from a database
        # For now, we just return a success message
        return {"message": f"History cleared for document {document_id}"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
