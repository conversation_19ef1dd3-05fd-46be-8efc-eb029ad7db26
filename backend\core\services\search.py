# backend/core/services/search.py
from typing import List, Optional
import numpy as np
from ..database import get_neo4j_session
from ..models.schemas import SearchResponse
from ..config import settings

try:
    from sklearn.metrics.pairwise import cosine_similarity
    from sentence_transformers import SentenceTransformer
    HAS_ML_DEPS = True
except ImportError:
    HAS_ML_DEPS = False
    print("Warning: Machine learning dependencies not found. Some search features will be limited.")

if HAS_ML_DEPS:
    # Initialize sentence transformer model
    model = SentenceTransformer('all-MiniLM-L6-v2')

async def semantic_search(
    document_id: str,
    query: str,
    limit: int = 5
) -> List[SearchResponse]:
    """Perform vector similarity search using embeddings"""
    if not HAS_ML_DEPS:
        raise ImportError("Semantic search requires scikit-learn and sentence-transformers")

    query_embedding = model.encode(query, convert_to_tensor=False)
    query_embedding = query_embedding.reshape(1, -1)

    with get_neo4j_session() as session:
        result = session.run(
            """
            MATCH (c:Chunk {document_id: $doc_id})
            WHERE c.embedding IS NOT NULL
            RETURN c.id as chunk_id, c.text as text, c.embedding as embedding
            LIMIT $limit * 2
            """,
            doc_id=document_id,
            limit=limit
        )
        chunks = [dict(record) for record in result]

    if not chunks:
        return []

    # Convert embeddings from bytes to numpy arrays
    chunk_embeddings = [np.frombuffer(chunk['embedding'], dtype=np.float32) for chunk in chunks]
    similarities = cosine_similarity(query_embedding, chunk_embeddings)[0]

    # Combine and sort results
    results = []
    for idx, chunk in enumerate(chunks):
        results.append({
            'chunk_id': chunk['chunk_id'],
            'text': chunk['text'],
            'score': float(similarities[idx])
        })

    results.sort(key=lambda x: x['score'], reverse=True)
    return [SearchResponse(**r) for r in results[:limit]]

async def keyword_search(
    document_id: str,
    query: str,
    limit: int = 5
) -> List[SearchResponse]:
    """Perform traditional keyword search"""
    keywords = [kw.strip().lower() for kw in query.split() if kw.strip()]

    with get_neo4j_session() as session:
        if keywords:
            # Build WHERE clause with OR conditions for any keyword match
            where_clause = " OR ".join(
                [f"toLower(c.text) CONTAINS '{kw}'" for kw in keywords]
            )
            query_str = f"""
                MATCH (c:Chunk {{document_id: $doc_id}})
                WHERE {where_clause}
                RETURN c.id as chunk_id, c.text as text
                LIMIT $limit
                """
        else:
            # If no keywords, return random chunks
            query_str = """
                MATCH (c:Chunk {document_id: $doc_id})
                RETURN c.id as chunk_id, c.text as text
                LIMIT $limit
                """

        result = session.run(
            query_str,
            doc_id=document_id,
            limit=limit
        )
        return [
            SearchResponse(
                chunk_id=record["chunk_id"],
                text=record["text"],
                score=1.0  # Default score for keyword matches
            )
            for record in result
        ]

async def hybrid_search(
    document_id: str,
    query: str,
    limit: int = 5
) -> List[SearchResponse]:
    """
    Fallback hybrid search that uses keyword search if ML dependencies aren't available
    """
    if HAS_ML_DEPS:
        # Get both result types if ML is available
        semantic_results = await semantic_search(document_id, query, limit)
        keyword_results = await keyword_search(document_id, query, limit)

        # Combine results with simple deduplication
        combined = {}
        for result in semantic_results + keyword_results:
            if result.chunk_id not in combined:
                combined[result.chunk_id] = result

        return list(combined.values())[:limit]
    else:
        # Fallback to keyword search only
        return await keyword_search(document_id, query, limit)

async def store_embeddings(document_id: str):
    """Store embeddings only if ML dependencies are available"""
    if not HAS_ML_DEPS:
        raise ImportError("Embedding storage requires scikit-learn and sentence-transformers")

    with get_neo4j_session() as session:
        result = session.run(
            """
            MATCH (c:Chunk {document_id: $doc_id})
            WHERE c.embedding IS NULL
            RETURN c.id as chunk_id, c.text as text
            """,
            doc_id=document_id
        )
        chunks = [dict(record) for record in result]

        for chunk in chunks:
            embedding = model.encode(chunk['text'])
            embedding_bytes = embedding.astype(np.float32).tobytes()

            session.run(
                """
                MATCH (c:Chunk {id: $chunk_id})
                SET c.embedding = $embedding
                """,
                chunk_id=chunk['chunk_id'],
                embedding=embedding_bytes
            )
