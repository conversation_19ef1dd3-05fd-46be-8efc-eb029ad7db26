import re
import nltk
from nltk.tokenize import sent_tokenize
import hashlib

def chunk_text(text: str, chunk_size: int = 2000, overlap: int = 200) -> list[str]:
    if not text:
        return []
    
    try:
        sentences = sent_tokenize(text)
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk) + len(sentence) > chunk_size and current_chunk:
                chunks.append(current_chunk)
                current_chunk = current_chunk[-overlap:] if len(current_chunk) > overlap else current_chunk
            current_chunk += " " + sentence
        
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    except Exception:
        return [text[i:i+chunk_size] for i in range(0, len(text), chunk_size - overlap)]

def calculate_document_hash(content: str, metadata: dict = None) -> str:
    hash_input = content
    if metadata:
        for key, value in sorted(metadata.items()):
            hash_input += f"|{key}:{value}"
    return hashlib.md5(hash_input.encode()).hexdigest()