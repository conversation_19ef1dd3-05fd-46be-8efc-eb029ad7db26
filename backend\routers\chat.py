# backend/routers/chat.py
from fastapi import APIRouter, HTTPException
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime
from core.services.llm import generate_answer
from core.models.model import QuestionRequest, AnswerResponse

router = APIRouter()

class ConversationEntry(BaseModel):
    question: str
    answer: str
    timestamp: datetime

class ConversationHistoryResponse(BaseModel):
    history: List[ConversationEntry]

@router.post("/ask", response_model=AnswerResponse)
async def ask_question(request: QuestionRequest):
    """Ask a question about a document"""
    try:
        answer = await generate_answer(request.document_id, request.question)

        # In a real implementation, you would store the conversation history in a database
        # For now, we just return the answer

        return {"answer": answer}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history/{document_id}", response_model=ConversationHistoryResponse)
async def get_conversation_history(document_id: str, limit: int = 10):
    """Get conversation history for a document"""
    try:
        # In a real implementation, you would retrieve the conversation history from a database
        # For now, we return an empty list

        return {"history": []}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))