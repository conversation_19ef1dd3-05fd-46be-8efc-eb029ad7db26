# backend/routers/files.py
from fastapi import APIRouter, HTTPException, UploadFile, File
from typing import List
from core.services.document import process_document, check_duplicate

router = APIRouter()

@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    extract_entities: bool = True
):
    """
    Upload a single file for processing

    - **file**: The file to upload (PDF, DOCX, or image)
    - **extract_entities**: Whether to extract entities and relationships (default: True)
    """
    try:
        return await process_document(file, extract_entities=extract_entities)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/batch-upload")
async def upload_multiple_files(
    files: List[UploadFile] = File(...),
    extract_entities: bool = True
):
    """
    Upload multiple files for processing

    - **files**: List of files to upload (PDF, DOCX, or images)
    - **extract_entities**: Whether to extract entities and relationships (default: True)
    """
    try:
        results = []
        for file in files:
            result = await process_document(file, extract_entities=extract_entities)
            results.append(result)
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/check-duplicate")
async def check_file_duplicate(file: UploadFile = File(...)):
    """Check if a file is a duplicate of an existing document"""
    try:
        return await check_duplicate(file)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
