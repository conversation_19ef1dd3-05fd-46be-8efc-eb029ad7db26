from neo4j import GraphDatabase
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv("backend/.env")

NEO4J_URI = os.getenv("NEO4J_URI")
NEO4J_USER = os.getenv("NEO4J_USER")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD")

print(f"Attempting to connect to Neo4j at: {NEO4J_URI}")
print(f"With user: {NEO4J_USER}")

try:
    # Initialize Neo4j driver
    driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
    
    # Test connection
    with driver.session() as session:
        result = session.run("RETURN 1 as num")
        record = result.single()
        print(f"Connection successful! Test query result: {record['num']}")
    
    # Close the driver
    driver.close()
    print("Connection closed.")
    
except Exception as e:
    print(f"Failed to connect to Neo4j: {str(e)}")
