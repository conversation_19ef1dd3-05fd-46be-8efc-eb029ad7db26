# backend/main.py
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from core.config import settings
from routers import chunks, entities, files, graph, history, search, chat, documents

logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup: Initialize resources
    import nltk
    nltk.download('punkt', quiet=True)
    logger.info("Application startup complete")

    yield

    # Shutdown: Clean up resources
    from core.database import driver
    if driver:
        driver.close()
    logger.info("Application shutdown complete")

app = FastAPI(
    title="Document Knowledge Graph API",
    description="API for document processing and knowledge graph extraction",
    version="1.0.0",
    debug=settings.DEBUG,
    lifespan=lifespan
)

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://**************:5177",
        "http://localhost:5177",
        "https://**************:5177",
        "https://localhost:5177",
        "http://localhost:3000",  # React dev server
        "http://localhost:5173",  # Vite dev server
        "*"  # Allow all origins (use with caution in production)
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Include all routers
app.include_router(documents.router, prefix=f"{settings.API_PREFIX}/documents", tags=["Documents"])
app.include_router(chat.router, prefix=f"{settings.API_PREFIX}/chat", tags=["Chat"])
app.include_router(graph.router, prefix=f"{settings.API_PREFIX}/graph", tags=["Graph"])
app.include_router(entities.router, prefix=f"{settings.API_PREFIX}/entities", tags=["Entities"])
app.include_router(chunks.router, prefix=f"{settings.API_PREFIX}/chunks", tags=["Chunks"])
app.include_router(files.router, prefix=f"{settings.API_PREFIX}/files", tags=["Files"])
app.include_router(history.router, prefix=f"{settings.API_PREFIX}/history", tags=["History"])
app.include_router(search.router, prefix=f"{settings.API_PREFIX}/search", tags=["Search"])

@app.get("/")
async def root():
    return {
        "message": "Welcome to the Document Knowledge Graph API",
        "docs": "/docs",
        "version": "1.0.0"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8001, reload=settings.DEBUG)