Stack trace:
Frame         Function      Args
0007FFFF5810  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF5810, 0007FFFF4710) msys-2.0.dll+0x1FE8E
0007FFFF5810  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF5AE8) msys-2.0.dll+0x67F9
0007FFFF5810  000210046832 (000210286019, 0007FFFF56C8, 0007FFFF5810, 000000000000) msys-2.0.dll+0x6832
0007FFFF5810  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF5810  000210068E24 (0007FFFF5820, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF5AF0  00021006A225 (0007FFFF5820, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE07CA0000 ntdll.dll
7FFE06930000 KERNEL32.DLL
7FFE055A0000 KERNELBASE.dll
7FFE06370000 USER32.dll
7FFE05330000 win32u.dll
7FFE06600000 GDI32.dll
7FFE051F0000 gdi32full.dll
7FFE05990000 msvcp_win.dll
7FFE04DF0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE06540000 advapi32.dll
7FFE07BB0000 msvcrt.dll
7FFE07360000 sechost.dll
7FFE06790000 RPCRT4.dll
7FFE043F0000 CRYPTBASE.DLL
7FFE04F40000 bcryptPrimitives.dll
7FFE07030000 IMM32.DLL
