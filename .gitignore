# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/
.coverage
htmlcov/

# Node.js / JavaScript
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log
.yarn-integrity
.npm
.eslintcache
.node_repl_history
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE / Editors
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store
.project
.classpath
.settings/
*.sublime-workspace
*.sublime-project

# Build outputs
/dist
/build
/out
/.next
/.nuxt
/.vuepress/dist
/.serverless
/.fusebox
/coverage

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Specific to this project
backend/venv/Lib/site-packages/colorama/__pycache__/win32.cpython-312.pyc