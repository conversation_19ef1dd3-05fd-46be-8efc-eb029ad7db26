from typing import List, Dict, Optional
from ..database import get_neo4j_session
from ..models.schemas import (
    EntityResponse,
    EntityRelationResponse,
    EntityChunkResponse
)

async def get_entities(document_id: str, limit: int = 100) -> List[EntityResponse]:
    """Get all entities in a document with their types"""
    with get_neo4j_session() as session:
        result = session.run(
            """
            MATCH (e:Entity {document_id: $doc_id})
            RETURN e.name as name, labels(e) as types
            LIMIT $limit
            """,
            doc_id=document_id,
            limit=limit
        )
        return [EntityResponse(name=record["name"], types=record["types"]) for record in result]

async def get_entity_relations(
    document_id: str,
    entity_name: str,
    depth: int = 1
) -> List[EntityRelationResponse]:
    """Get relationships for a specific entity"""
    with get_neo4j_session() as session:
        result = session.run(
            """
            MATCH path=(e:Entity {name: $name, document_id: $doc_id})-[*1..$depth]-(related)
            UNWIND relationships(path) as rel
            RETURN startNode(rel).name as from_entity,
                   type(rel) as relationship,
                   endNode(rel).name as to_entity
            """,
            name=entity_name,
            doc_id=document_id,
            depth=depth
        )
        return [
            EntityRelationResponse(
                from_entity=record["from_entity"],
                relationship=record["relationship"],
                to_entity=record["to_entity"]
            )
            for record in result
        ]

async def get_entity_chunks(
    document_id: str,
    entity_name: str
) -> List[EntityChunkResponse]:
    """Get chunks containing a specific entity"""
    with get_neo4j_session() as session:
        result = session.run(
            """
            MATCH (e:Entity {name: $name, document_id: $doc_id})-[:APPEARS_IN]->(c:Chunk)
            RETURN c.id as chunk_id, c.text as text
            """,
            name=entity_name,
            doc_id=document_id
        )
        return [EntityChunkResponse(chunk_id=record["chunk_id"], text=record["text"]) for record in result]