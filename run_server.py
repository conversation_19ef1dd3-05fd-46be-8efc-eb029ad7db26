import os
import sys
import subprocess

# Change to the backend directory
os.chdir('backend')

# Run the uvicorn command
try:
    print("Starting FastAPI server...")
    subprocess.run([
        sys.executable, 
        "-m", 
        "uvicorn", 
        "main:app", 
        "--host", 
        "0.0.0.0", 
        "--port", 
        "8000", 
        "--reload"
    ], check=True)
except subprocess.CalledProcessError as e:
    print(f"Error running server: {e}")
except KeyboardInterrupt:
    print("Server stopped by user")
