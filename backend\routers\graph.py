# backend/routers/graph.py
from fastapi import APIRouter, HTTPException
from typing import List, Optional
from pydantic import BaseModel
from core.services.graph import get_knowledge_graph, execute_cypher_query
from core.services.llm import generate_cypher_query

router = APIRouter()

class KeywordRequest(BaseModel):
    keywords: List[str] = []

class CypherRequest(BaseModel):
    query: str

class QueryGenerationRequest(BaseModel):
    document_id: str
    question: str

@router.get("/{document_id}")
async def get_document_graph(document_id: str):
    """Get the knowledge graph for a document"""
    try:
        return await get_knowledge_graph(document_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{document_id}/filter")
async def filter_document_graph(document_id: str, request: KeywordRequest):
    """Filter the knowledge graph by keywords"""
    try:
        return await get_knowledge_graph(document_id, request.keywords)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/query")
async def run_cypher_query(request: CypherRequest):
    """Execute a custom Cypher query"""
    try:
        return await execute_cypher_query(request.query)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/generate-query")
async def generate_query(request: QueryGenerationRequest):
    """Generate a Cypher query from a natural language question"""
    try:
        query, error = await generate_cypher_query(request.document_id, request.question)
        if error:
            return {"query": query, "error": error, "is_fallback": True}
        return {"query": query, "error": None, "is_fallback": False}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
