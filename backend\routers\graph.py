# backend/routers/graph.py
from fastapi import APIRouter, HTTPException
from typing import List, Optional
from pydantic import BaseModel
from core.services.graph import get_knowledge_graph, execute_cypher_query
from core.services.llm import generate_cypher_query

router = APIRouter()

class KeywordRequest(BaseModel):
    keywords: List[str] = []

class CypherRequest(BaseModel):
    query: str

class QueryGenerationRequest(BaseModel):
    document_id: str
    question: str

@router.get("/{document_id}")
async def get_document_graph(document_id: str):
    """Get the knowledge graph for a document"""
    try:
        return await get_knowledge_graph(document_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{document_id}/filter")
async def filter_document_graph(document_id: str, request: KeywordRequest):
    """Filter the knowledge graph by keywords"""
    try:
        return await get_knowledge_graph(document_id, request.keywords)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/query")
async def run_cypher_query(request: CypherRequest):
    """Execute a custom Cypher query"""
    try:
        return await execute_cypher_query(request.query)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/generate-query")
async def generate_query(request: QueryGenerationRequest):
    """Generate a Cypher query from a natural language question"""
    try:
        query, error = await generate_cypher_query(request.document_id, request.question)
        if error:
            return {"query": query, "error": error, "is_fallback": True}
        return {"query": query, "error": None, "is_fallback": False}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/debug/{document_id}")
async def debug_document_graph(document_id: str):
    """Debug endpoint to check what's in the database for a document"""
    try:
        from core.database import get_neo4j_session

        with get_neo4j_session() as session:
            # Check if document exists
            doc_result = session.run(
                "MATCH (d:Document {id: $document_id}) RETURN d.id as id, d.name as name, d.has_entities as has_entities",
                document_id=document_id
            )
            doc_record = doc_result.single()

            if not doc_record:
                return {"error": f"Document {document_id} not found"}

            # Count entities
            entity_result = session.run(
                "MATCH (e:Entity {document_id: $document_id}) RETURN count(e) as count",
                document_id=document_id
            )
            entity_count = entity_result.single()["count"]

            # Count relationships
            rel_result = session.run(
                "MATCH (e1:Entity {document_id: $document_id})-[r:RELATION]->(e2:Entity {document_id: $document_id}) RETURN count(r) as count",
                document_id=document_id
            )
            rel_count = rel_result.single()["count"]

            # Get sample entities
            sample_entities = session.run(
                "MATCH (e:Entity {document_id: $document_id}) RETURN e.name as name LIMIT 5",
                document_id=document_id
            )
            entities = [record["name"] for record in sample_entities]

            # Get sample relationships
            sample_rels = session.run(
                "MATCH (e1:Entity {document_id: $document_id})-[r:RELATION]->(e2:Entity {document_id: $document_id}) RETURN e1.name as from, r.type as rel, e2.name as to LIMIT 5",
                document_id=document_id
            )
            relationships = [{"from": record["from"], "relation": record["rel"], "to": record["to"]} for record in sample_rels]

            return {
                "document": {
                    "id": doc_record["id"],
                    "name": doc_record["name"],
                    "has_entities": doc_record["has_entities"]
                },
                "entity_count": entity_count,
                "relationship_count": rel_count,
                "sample_entities": entities,
                "sample_relationships": relationships
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
