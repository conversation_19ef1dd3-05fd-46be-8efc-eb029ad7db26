
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from typing import List
from core.services.document import (
    process_document,
    get_document,
    delete_document,
    list_documents,
    check_duplicate
)
from core.models.schemas import (
    DocumentResponse,
    DocumentListResponse,
    DuplicateCheckResponse
)

router = APIRouter()

@router.post("/upload", response_model=DocumentResponse)
async def upload_file(
    file: UploadFile = File(...),
    extract_entities: bool = True
):
    """
    Upload a single file for processing

    - **file**: The file to upload (PDF, DOCX, XLSX, SVG, or image)
    - **extract_entities**: Whether to extract entities and relationships (default: True)
    """
    try:
        return await process_document(file, extract_entities=extract_entities)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/batch-upload", response_model=List[DocumentResponse])
async def upload_files(
    files: List[UploadFile] = File(...),
    extract_entities: bool = True
):
    """
    Upload multiple files for processing

    - **files**: List of files to upload (PDF, DOCX, XLSX, SVG, or images)
    - **extract_entities**: Whether to extract entities and relationships (default: True)
    """
    try:
        return [await process_document(file, extract_entities=extract_entities) for file in files]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document_by_id(document_id: str):
    try:
        return await get_document(document_id)
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.delete("/{document_id}")
async def delete_document_by_id(document_id: str):
    try:
        await delete_document(document_id)
        return {"status": "success"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/", response_model=DocumentListResponse)
async def list_all_documents():
    try:
        return await list_documents()
    except Exception as e:
        import traceback
        error_details = f"Error: {str(e)}\n{traceback.format_exc()}"
        print(error_details)  # Print to console for debugging
        raise HTTPException(status_code=500, detail=error_details)

@router.post("/check-duplicate", response_model=DuplicateCheckResponse)
async def check_duplicate_document(file: UploadFile = File(...)):
    try:
        return await check_duplicate(file)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))