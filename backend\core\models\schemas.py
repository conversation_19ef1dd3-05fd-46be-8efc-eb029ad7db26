from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class ChunkResponse(BaseModel):
    id: str
    text: str
    index: int
    document_id: Optional[str] = None

    class Config:
        from_attributes = True

class EntityResponse(BaseModel):
    name: str
    types: List[str]

class EntityRelationResponse(BaseModel):
    from_entity: str
    relationship: str
    to_entity: str

class EntityChunkResponse(BaseModel):
    chunk_id: str
    text: str

class SearchResponse(BaseModel):
    chunk_id: str
    text: str
    score: float

class SearchRequest(BaseModel):
    document_id: str
    query: str
    limit: int = 5

# Document-related models
class DocumentResponse(BaseModel):
    document_id: str
    text_preview: str = ""  # Default to empty string if None
    metadata: Optional[Dict[str, Any]] = None

class DocumentListResponse(BaseModel):
    documents: List[DocumentResponse]

class DuplicateCheckResponse(BaseModel):
    is_duplicate: bool
    existing_document: Optional[Dict[str, Any]] = None
    hash: str