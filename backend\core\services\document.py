# import uuid
# from typing import <PERSON><PERSON>
# from PyPDF2 import Pdf<PERSON><PERSON><PERSON>
# from docx import Document as DocxDocument
# import pytesseract
# from PIL import Image
# from backend.core.database import get_neo4j_session

# async def extract_text_from_pdf(file):
#     reader = PdfReader(file)
#     return "\n".join([page.extract_text() for page in reader.pages if page.extract_text()])

# async def process_document(file) -> Tuple[str, str]:
#     file_type = file.filename.split(".")[-1].lower()

#     if file_type == "pdf":
#         text = await extract_text_from_pdf(file.file)
#     elif file_type == "docx":
#         doc = DocxDocument(file.file)
#         text = "\n".join([para.text for para in doc.paragraphs])
#     elif file_type in ["png", "jpg", "jpeg"]:
#         img = Image.open(file.file)
#         text = pytesseract.image_to_string(img)
#     else:
#         raise ValueError("Unsupported file type")

#     doc_id = str(uuid.uuid4())

#     # Store in Neo4j
#     with get_neo4j_session() as session:
#         session.run(
#             "CREATE (d:Document {id: $id, text: $text})",
#             id=doc_id, text=text[:1000]  # Store first 1000 chars
#         )

#     return doc_id, text# backend/core/services/document.py
import uuid
import logging
from typing import List, Dict, Optional
from ..database import get_neo4j_session
from ..utils.file_processing import extract_text_from_file
from ..utils.text_processing import chunk_text, calculate_document_hash
from .entity_extraction import extract_entities_with_openai, populate_neo4j
from ..models.schemas import (
    DocumentResponse,
    DocumentListResponse,
    DuplicateCheckResponse
)

logger = logging.getLogger(__name__)

async def process_document(file, extract_entities: bool = True) -> DocumentResponse:
    """
    Process a document file:
    1. Extract text from the file
    2. Split text into chunks
    3. Store document and chunks in Neo4j
    4. Optionally extract entities and relationships

    Args:
        file: The uploaded file
        extract_entities: Whether to extract entities and relationships (default: True)

    Returns:
        DocumentResponse with document ID and metadata
    """
    file_type = file.filename.split(".")[-1].lower()
    doc_id = str(uuid.uuid4())

    # Extract text
    text = await extract_text_from_file(file, file_type)

    # Process chunks
    chunks = chunk_text(text)

    # Calculate hash
    doc_hash = calculate_document_hash(text, {
        "filename": file.filename,
        "size": file.size,
        "type": file_type
    })

    # Store in Neo4j
    with get_neo4j_session() as session:
        # Store document metadata
        session.run(
            """
            CREATE (d:Document {
                id: $id,
                name: $name,
                type: $type,
                hash: $hash,
                text_preview: $preview,
                chunk_count: $chunk_count,
                created_at: datetime(),
                has_entities: $has_entities
            })
            """,
            id=doc_id,
            name=file.filename,
            type=file_type,
            hash=doc_hash,
            preview=text[:500],
            chunk_count=len(chunks),
            has_entities=extract_entities
        )

        # Store chunks
        chunk_ids = []
        for i, chunk in enumerate(chunks):
            chunk_id = f"{doc_id}_{i}"
            chunk_ids.append(chunk_id)
            session.run(
                """
                MATCH (d:Document {id: $doc_id})
                CREATE (c:Chunk {
                    id: $chunk_id,
                    index: $index,
                    text: $text,
                    document_id: $doc_id
                })
                CREATE (d)-[:HAS_CHUNK]->(c)
                """,
                doc_id=doc_id,
                chunk_id=chunk_id,
                index=i,
                text=chunk[:1000]
            )

        # Extract entities if enabled
        if extract_entities:
            logger.info(f"Extracting entities from document {doc_id} in a single call")

            # Use a sample of the document if it's too large (to avoid token limits)
            sample_text = text[:10000] if len(text) > 10000 else text
            triples_text = await extract_entities_with_openai(sample_text, doc_id)

            # Populate Neo4j with extracted knowledge (associate with the document, not chunks)
            await populate_neo4j(triples_text, doc_id)
        else:
            logger.info(f"Entity extraction disabled for document {doc_id}")

    return DocumentResponse(
        document_id=doc_id,
        text_preview=text[:200],
        metadata={
            "filename": file.filename,
            "type": file_type,
            "chunk_count": len(chunks),
            "hash": doc_hash
        }
    )

async def get_document(document_id: str) -> DocumentResponse:
    with get_neo4j_session() as session:
        result = session.run(
            """
            MATCH (d:Document {id: $id})
            RETURN d.id as id, d.name as name, d.type as type,
                   d.text_preview as preview, d.chunk_count as chunk_count,
                   d.created_at as created_at
            """,
            id=document_id
        )
        record = result.single()

        if not record:
            raise ValueError("Document not found")

        return DocumentResponse(
            document_id=record["id"],
            text_preview=record["preview"],
            metadata={
                "filename": record["name"],
                "type": record["type"],
                "chunk_count": record["chunk_count"],
                "created_at": record["created_at"]
            }
        )

async def delete_document(document_id: str):
    with get_neo4j_session() as session:
        session.run(
            """
            MATCH (d:Document {id: $id})
            DETACH DELETE d
            """,
            id=document_id
        )

async def list_documents() -> DocumentListResponse:
    try:
        with get_neo4j_session() as session:
            result = session.run(
                """
                MATCH (d:Document)
                OPTIONAL MATCH (d)-[:HAS_CHUNK]->(c:Chunk)
                WITH d, count(c) as actual_chunk_count,
                     collect(c.text)[0..1] as sample_texts
                RETURN d.id as id, d.name as name, d.type as type,
                       d.text_preview as preview,
                       COALESCE(d.chunk_count, actual_chunk_count) as chunk_count,
                       d.created_at as created_at,
                       d.hash as hash,
                       d.metadata as metadata,
                       sample_texts
                ORDER BY d.created_at DESC
                """
            )

            # Convert to list to avoid cursor issues
            records = list(result)

            # For debugging
            print(f"Found {len(records)} documents in Neo4j")

            documents = []
            for record in records:
                try:
                    # Convert record to dict for easier handling
                    record_dict = dict(record)

                    # Handle potential None values or missing fields
                    doc_id = record_dict.get("id")
                    if not doc_id:
                        print(f"Skipping document with no ID: {record_dict}")
                        continue

                    # Ensure text_preview is a string
                    preview = record_dict.get("preview")
                    if preview is None or preview == "":
                        # Try to get preview from sample texts
                        sample_texts = record_dict.get("sample_texts", [])
                        if sample_texts and len(sample_texts) > 0 and sample_texts[0]:
                            preview = sample_texts[0][:200] + "..." if len(sample_texts[0]) > 200 else sample_texts[0]
                        else:
                            preview = ""

                    # Get other fields with defaults
                    name = record_dict.get("name")
                    if name is None:
                        # Try to get name from metadata
                        metadata = record_dict.get("metadata", {})
                        if metadata and isinstance(metadata, dict) and metadata.get("filename"):
                            name = metadata.get("filename")
                        else:
                            name = f"Document {doc_id[:8]}"

                    doc_type = record_dict.get("type")
                    if doc_type is None:
                        # Try to get type from metadata
                        metadata = record_dict.get("metadata", {})
                        if metadata and isinstance(metadata, dict) and metadata.get("type"):
                            doc_type = metadata.get("type")
                        else:
                            doc_type = "Unknown"

                    chunk_count = record_dict.get("chunk_count")
                    if chunk_count is None:
                        chunk_count = 0

                    # Convert Neo4j DateTime to Python datetime or None
                    created_at = record_dict.get("created_at")
                    if created_at is not None:
                        try:
                            # Convert Neo4j DateTime to ISO format string
                            created_at = created_at.isoformat()
                        except Exception as e:
                            print(f"Error converting datetime: {str(e)}")
                            created_at = None

                    # Get document hash
                    doc_hash = record_dict.get("hash")

                    # Build metadata dictionary
                    metadata_dict = {
                        "filename": name,
                        "type": doc_type,
                        "chunk_count": chunk_count,
                        "created_at": created_at,
                        "hash": doc_hash
                    }

                    # Add any additional metadata from the database
                    db_metadata = record_dict.get("metadata")
                    if db_metadata and isinstance(db_metadata, dict):
                        for key, value in db_metadata.items():
                            if key not in metadata_dict:
                                metadata_dict[key] = value

                    # Create document response
                    doc_response = DocumentResponse(
                        document_id=doc_id,
                        text_preview=preview,
                        metadata=metadata_dict
                    )

                    documents.append(doc_response)

                except Exception as e:
                    # Log the error but continue processing other records
                    import logging
                    logging.error(f"Error processing document record: {str(e)}")
                    print(f"Error processing document: {str(e)}")
                    continue

            print(f"Successfully processed {len(documents)} documents")
            return DocumentListResponse(documents=documents)
    except Exception as e:
        import traceback
        error_msg = f"Error listing documents: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)  # Print to console for debugging

        # Return empty list instead of raising an exception
        return DocumentListResponse(documents=[])

async def check_duplicate(file) -> DuplicateCheckResponse:
    file_type = file.filename.split(".")[-1].lower()
    text = await extract_text_from_file(file, file_type)
    doc_hash = calculate_document_hash(text, {
        "filename": file.filename,
        "size": file.size,
        "type": file_type
    })

    with get_neo4j_session() as session:
        result = session.run(
            """
            MATCH (d:Document {hash: $hash})
            RETURN d.id as id, d.name as name
            LIMIT 1
            """,
            hash=doc_hash
        )
        record = result.single()

        return DuplicateCheckResponse(
            is_duplicate=bool(record),
            existing_document={
                "id": record["id"] if record else None,
                "name": record["name"] if record else None
            } if record else None,
            hash=doc_hash
        )