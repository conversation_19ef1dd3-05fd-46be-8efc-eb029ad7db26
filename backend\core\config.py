# backend/core/config.py
from pydantic_settings import BaseSettings
from dotenv import load_dotenv
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Load environment variables from .env file
load_dotenv()

class Settings(BaseSettings):
    NEO4J_URI: str
    NEO4J_USER: str
    NEO4J_PASSWORD: str
    OPENAI_API_KEY: str

    # API settings
    API_PREFIX: str = "/api"
    DEBUG: bool = False

    # CORS settings
    CORS_ORIGINS: list = ["*"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: list = ["*"]
    CORS_ALLOW_HEADERS: list = ["*"]

    class Config:
        env_file = ".env"  # Explicitly tell Pydantic where to find .env

settings = Settings()

# Log configuration on startup (but not sensitive data)
logger = logging.getLogger(__name__)
logger.info(f"API running with prefix: {settings.API_PREFIX}")
logger.info(f"Debug mode: {settings.DEBUG}")
logger.info(f"Connected to Neo4j database")