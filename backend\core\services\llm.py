
# backend/core/services/llm.py
import logging
from typing import List, Dict, Tuple, Optional
from langchain.schema import HumanMessage
from ..config import settings
from ..database import get_neo4j_session
from langchain_openai import ChatOpenAI  # Updated import
from .entity_extraction import extract_keywords_from_question
from .search import hybrid_search

logger = logging.getLogger(__name__)

# Initialize OpenAI client
llm = ChatOpenAI(openai_api_key=settings.OPENAI_API_KEY)

async def generate_answer(document_id: str, question: str) -> str:
    """
    Generate an answer to a question using RAG (Retrieval Augmented Generation)

    1. Extract keywords from the question
    2. Search for relevant chunks using hybrid search
    3. Generate an answer based on the retrieved chunks
    """
    try:
        # First, check if the document exists
        with get_neo4j_session() as session:
            doc_result = session.run(
                """
                MATCH (d:Document {id: $id})
                RETURN d.id, d.text_preview AS preview, d.name AS name
                """,
                id=document_id
            )
            doc_record = doc_result.single()

            if not doc_record:
                return f"Document with ID {document_id} not found."

        # Get document chunks directly (fallback if search fails)
        with get_neo4j_session() as session:
            chunks_result = session.run(
                """
                MATCH (d:Document {id: $id})-[:HAS_CHUNK]->(c:Chunk)
                RETURN c.id as chunk_id, c.text as text
                ORDER BY c.index
                LIMIT 5
                """,
                id=document_id
            )
            chunks = [dict(record) for record in chunks_result]

        # Try to get relevant chunks using hybrid search
        try:
            search_results = await hybrid_search(document_id, question, limit=5)
            # Extract relevant context from search results
            context = "\n\n".join([f"Chunk {i+1}:\n{result.text}" for i, result in enumerate(search_results)])
        except Exception as search_error:
            logger.warning(f"Search failed, using direct chunks: {str(search_error)}")
            # Use direct chunks if search fails
            context = "\n\n".join([f"Chunk {i+1}:\n{chunk['text']}" for i, chunk in enumerate(chunks)])

        # If still no context, use document preview
        if not context and doc_record:
            context = f"Document preview: {doc_record['preview']}\nDocument name: {doc_record['name']}"

        # If still no context, return error
        if not context:
            return "I couldn't find any relevant information to answer your question."

        # Generate answer using OpenAI
        prompt = f"""
        Answer the following question based on the provided context.
        If the context doesn't contain enough information to answer the question,
        say that you don't have enough information.

        Context:
        {context}

        Question: {question}
        """

        response = llm([HumanMessage(content=prompt)])
        return response.content

    except Exception as e:
        logger.error(f"Error generating answer: {str(e)}")
        return f"An error occurred while generating an answer: {str(e)}"

async def generate_cypher_query(document_id: str, question: str) -> Tuple[str, Optional[str]]:
    """Generate a Cypher query to answer a question using the knowledge graph"""
    try:
        # Extract keywords from the question
        keywords = await extract_keywords_from_question(question)

        cypher_prompt = f"""
        Given the user question: "{question}", write a Cypher query that could help answer it using a graph of entities and relationships.

        The graph schema is:
        - Nodes have label 'Entity' with properties 'name' and 'document_id'
        - Relationships have type 'RELATION' with property 'type' containing the relation name
        - Nodes are connected to Chunk nodes via APPEARS_IN relationships
        - Chunk nodes have 'id', 'document_id', and 'text' properties

        Important keywords from the question: {', '.join(keywords)}

        Return ONLY the Cypher query without any explanation.
        """

        response = llm([HumanMessage(content=cypher_prompt)])
        cypher = response.content.strip()

        # Ensure the query includes the document_id filter
        if "document_id" not in cypher and "{" not in cypher:
            # Add document_id filter if not present
            if "WHERE" in cypher:
                cypher = cypher.replace("WHERE", f"WHERE e.document_id = '{document_id}' AND ")
            elif "MATCH" in cypher:
                match_clause_end = cypher.find(")", cypher.find("MATCH")) + 1
                cypher = cypher[:match_clause_end] + f" WHERE e.document_id = '{document_id}'" + cypher[match_clause_end:]

        return cypher, None

    except Exception as e:
        # Return a simple fallback query
        fallback_query = f"""
        MATCH (e:Entity)
        WHERE e.document_id = '{document_id}'
        OPTIONAL MATCH (e)-[:APPEARS_IN]->(c:Chunk)
        RETURN e.name AS entity, c.text AS context
        LIMIT 10
        """
        return fallback_query, str(e)