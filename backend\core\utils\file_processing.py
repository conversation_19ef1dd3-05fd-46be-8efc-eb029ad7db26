from PyPDF2 import Pdf<PERSON>eader
from docx import Document as DocxDocument
import pytesseract
from PIL import Image
from io import BytesIO
import logging
from openpyxl import load_workbook
from bs4 import BeautifulSoup
import re

logger = logging.getLogger(__name__)

async def extract_text_from_file(file, file_type: str) -> str:
    try:
        file_content = await file.read()
        file_bytes = BytesIO(file_content)

        if file_type == "pdf":
            return await _extract_text_from_pdf(file_bytes)
        elif file_type == "docx":
            return await _extract_text_from_docx(file_bytes)
        elif file_type in ["png", "jpg", "jpeg"]:
            return await _extract_text_from_image(file_bytes)
        elif file_type == "xlsx":
            return await _extract_text_from_xlsx(file_bytes)
        elif file_type == "svg":
            return await _extract_text_from_svg(file_content.decode('utf-8'))
        else:
            raise ValueError(f"Unsupported file type: {file_type}")
    except Exception as e:
        logger.error(f"Error extracting text: {str(e)}")
        raise

async def _extract_text_from_pdf(file_bytes) -> str:
    reader = PdfReader(file_bytes)
    return "\n".join([page.extract_text() for page in reader.pages if page.extract_text()])

async def _extract_text_from_docx(file_bytes) -> str:
    doc = DocxDocument(file_bytes)
    return "\n".join([para.text for para in doc.paragraphs])

async def _extract_text_from_image(file_bytes) -> str:
    img = Image.open(file_bytes)
    return pytesseract.image_to_string(img)

async def _extract_text_from_xlsx(file_bytes) -> str:
    """Extract text from Excel files (.xlsx)"""
    try:
        workbook = load_workbook(file_bytes, data_only=True)
        text_content = []

        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]
            text_content.append(f"Sheet: {sheet_name}")

            for row in sheet.iter_rows(values_only=True):
                row_text = []
                for cell in row:
                    if cell is not None:
                        row_text.append(str(cell))
                if row_text:  # Only add non-empty rows
                    text_content.append(" | ".join(row_text))

        return "\n".join(text_content)
    except Exception as e:
        logger.error(f"Error extracting text from XLSX: {str(e)}")
        return ""

async def _extract_text_from_svg(svg_content: str) -> str:
    """Extract text from SVG files"""
    try:
        soup = BeautifulSoup(svg_content, 'xml')

        # Extract text from <text> elements
        text_elements = soup.find_all('text')
        text_content = []

        for text_elem in text_elements:
            if text_elem.string:
                text_content.append(text_elem.string.strip())
            else:
                # Handle nested elements within text
                text_content.append(text_elem.get_text().strip())

        # Extract text from <title> and <desc> elements
        title = soup.find('title')
        if title and title.string:
            text_content.insert(0, f"Title: {title.string.strip()}")

        desc = soup.find('desc')
        if desc and desc.string:
            text_content.insert(-1 if title else 0, f"Description: {desc.string.strip()}")

        # Clean up and join text
        cleaned_text = []
        for text in text_content:
            if text and text.strip():
                cleaned_text.append(text.strip())

        return "\n".join(cleaned_text) if cleaned_text else "No text content found in SVG"
    except Exception as e:
        logger.error(f"Error extracting text from SVG: {str(e)}")
        return ""