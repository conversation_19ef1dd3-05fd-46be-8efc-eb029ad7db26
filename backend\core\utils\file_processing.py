from PyPDF2 import Pd<PERSON><PERSON>ead<PERSON>
from docx import Document as DocxDocument
import pytesseract
from PIL import Image
from io import BytesIO
import logging

logger = logging.getLogger(__name__)

async def extract_text_from_file(file, file_type: str) -> str:
    try:
        file_content = await file.read()
        file_bytes = BytesIO(file_content)
        
        if file_type == "pdf":
            return await _extract_text_from_pdf(file_bytes)
        elif file_type == "docx":
            return await _extract_text_from_docx(file_bytes)
        elif file_type in ["png", "jpg", "jpeg"]:
            return await _extract_text_from_image(file_bytes)
        else:
            raise ValueError(f"Unsupported file type: {file_type}")
    except Exception as e:
        logger.error(f"Error extracting text: {str(e)}")
        raise

async def _extract_text_from_pdf(file_bytes) -> str:
    reader = PdfReader(file_bytes)
    return "\n".join([page.extract_text() for page in reader.pages if page.extract_text()])

async def _extract_text_from_docx(file_bytes) -> str:
    doc = DocxDocument(file_bytes)
    return "\n".join([para.text for para in doc.paragraphs])

async def _extract_text_from_image(file_bytes) -> str:
    img = Image.open(file_bytes)
    return pytesseract.image_to_string(img)