# backend/routers/entities.py
from fastapi import APIRouter, HTTPException
from typing import List
from core.services.entity import (
    get_entities,
    get_entity_relations,
    get_entity_chunks
)
from core.models.schemas import (
    EntityResponse,
    EntityRelationResponse,
    EntityChunkResponse
)

router = APIRouter()

@router.get("/{document_id}", response_model=List[EntityResponse])
async def get_document_entities(document_id: str, limit: int = 100):
    try:
        return await get_entities(document_id, limit)
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.get("/{document_id}/{entity_name}", response_model=List[EntityRelationResponse])
async def get_entity_relationships(
    document_id: str,
    entity_name: str,
    depth: int = 1
):
    try:
        return await get_entity_relations(document_id, entity_name, depth)
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.get("/{document_id}/{entity_name}/chunks", response_model=List[EntityChunkResponse])
async def get_chunks_containing_entity(
    document_id: str,
    entity_name: str
):
    try:
        return await get_entity_chunks(document_id, entity_name)
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))