# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
.env
venv/
env/
venv/
ENV/
env.bak/
venv.bak/

# Testing
.pytest_cache/
.coverage
htmlcov/

# IDE / Editors
.idea/
.vscode/
*.swp
*.swo
*~

# Logs
logs/
*.log

# Local development
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local