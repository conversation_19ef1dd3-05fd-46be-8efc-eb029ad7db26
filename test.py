import os
import streamlit as st
from dotenv import load_dotenv
from neo4j import GraphDatabase
from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
from langchain.embeddings import OpenAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.vectorstores import FAISS
from langchain.chains import Convers<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>hain
from transformers import AutoToken<PERSON>, AutoModelForCausalLM
import torch
from uuid import uuid4
from PyPDF2 import PdfReader
from docx import Document as DocxDocument
from PIL import Image
import pytesseract
import networkx as nx
from pyvis.network import Network
import streamlit.components.v1 as components
import tempfile
from datetime import datetime
import re
import nltk
from nltk.tokenize import sent_tokenize
import textwrap
import hashlib

# Define a simple Document class
class Document:
    def __init__(self, page_content, metadata=None):
        self.page_content = page_content
        self.metadata = metadata or {}
        # Generate a unique ID for this document
        self.id = str(uuid4())

# UI Layout
st.title("Document Knowledge Graph RAG")
st.sidebar.title("Chat History")

# Load environment variables
load_dotenv()
NEO4J_URI = os.getenv("NEO4J_URI")
NEO4J_USER = os.getenv("NEO4J_USER")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# Initialize Neo4j driver
connection_status = st.sidebar.empty()
try:
    driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
    # Test connection
    with driver.session() as session:
        session.run("RETURN 1")
    connection_status.success("Connected to Neo4j")
except Exception as e:
    connection_status.error(f"Failed to connect to Neo4j: {str(e)}")
    driver = None

# Initialize OpenAI Chat model
openai_status = st.sidebar.empty()
try:
    chat = ChatOpenAI(openai_api_key=OPENAI_API_KEY)
    openai_status.success("Connected to OpenAI")
except Exception as e:
    openai_status.error(f"Failed to connect to OpenAI: {str(e)}")
    chat = None

# Initialize OpenAI embeddings
embeddings = OpenAIEmbeddings(openai_api_key=OPENAI_API_KEY)

# Download NLTK resources if needed
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt', quiet=True)

# Initialize session state
if "history" not in st.session_state:
    st.session_state.history = []
if "file_ids" not in st.session_state:
    st.session_state.file_ids = []
if "file_names" not in st.session_state:
    st.session_state.file_names = {}
if "file_hashes" not in st.session_state:
    st.session_state.file_hashes = {}  # Store document hashes for duplicate detection
if "file_metadata" not in st.session_state:
    st.session_state.file_metadata = {}  # Store metadata about documents
if "current_file_id" not in st.session_state:
    st.session_state.current_file_id = None
if "chunks" not in st.session_state:
    st.session_state.chunks = {}  # Store text chunks by file_id
if "chunk_metadata" not in st.session_state:
    st.session_state.chunk_metadata = {}  # Store metadata about chunks
if "vector_stores" not in st.session_state:
    st.session_state.vector_stores = {}  # Store FAISS vector stores by file_id
if "conversation_history" not in st.session_state:
    st.session_state.conversation_history = []  # Store conversation history for context awareness

# Utility Functions
def extract_text_from_pdf(file):
    try:
        reader = PdfReader(file)
        return "\n".join([page.extract_text() for page in reader.pages if page.extract_text()])
    except Exception as e:
        st.error(f"Error extracting text from PDF: {str(e)}")
        return ""

def extract_text_from_docx(file):
    try:
        doc = DocxDocument(file)
        return "\n".join([para.text for para in doc.paragraphs])
    except Exception as e:
        st.error(f"Error extracting text from DOCX: {str(e)}")
        return ""

def extract_text_from_image(file):
    try:
        img = Image.open(file)
        return pytesseract.image_to_string(img)
    except Exception as e:
        st.error(f"Error extracting text from image: {str(e)}")
        return ""

def chunk_text(text, chunk_size=2000, overlap=200):
    """Split text into overlapping chunks of approximately chunk_size characters"""
    if not text:
        return []

    # First try to split by sentences to preserve context
    try:
        sentences = sent_tokenize(text)
        chunks = []
        current_chunk = ""

        for sentence in sentences:
            # If adding this sentence would exceed chunk_size, save current chunk and start a new one
            if len(current_chunk) + len(sentence) > chunk_size and current_chunk:
                chunks.append(current_chunk)
                # Keep some overlap for context
                current_chunk = current_chunk[-overlap:] if len(current_chunk) > overlap else current_chunk

            current_chunk += " " + sentence

        # Add the last chunk if it's not empty
        if current_chunk:
            chunks.append(current_chunk)

        return chunks
    except Exception as e:
        # Fallback to simple text wrapping if sentence tokenization fails
        st.warning(f"Sentence tokenization failed: {str(e)}. Using fallback chunking method.")
        return textwrap.wrap(text, chunk_size, break_long_words=False, replace_whitespace=False)

def calculate_document_hash(content, metadata=None):
    """
    Calculate a hash for a document based on its content and metadata

    Args:
        content: The text content of the document
        metadata: Optional dictionary of metadata about the document

    Returns:
        A hash string that uniquely identifies the document
    """
    # Create a string that combines content and metadata
    hash_input = content

    # Add metadata if provided
    if metadata:
        for key, value in sorted(metadata.items()):
            hash_input += f"|{key}:{value}"

    # Calculate MD5 hash
    return hashlib.md5(hash_input.encode()).hexdigest()

def is_duplicate_document(content, metadata=None):
    """
    Check if a document with the same content and metadata has already been processed

    Args:
        content: The text content of the document
        metadata: Optional dictionary of metadata about the document

    Returns:
        (bool, str): Tuple of (is_duplicate, existing_file_id)
    """
    doc_hash = calculate_document_hash(content, metadata)

    # Check if this hash exists in our file_hashes
    if doc_hash in st.session_state.file_hashes:
        return True, st.session_state.file_hashes[doc_hash]

    return False, None

def generate_chunk_id(text, file_id, chunk_index):
    """Generate a unique ID for a chunk based on its content and metadata"""
    content_hash = hashlib.md5(text.encode()).hexdigest()[:8]
    return f"{file_id}_{chunk_index}_{content_hash}"

def extract_entities_with_openai(text, chunk_id=None):
    """
    Extract entities and relationships from text using OpenAI

    Args:
        text: The text to extract entities from
        chunk_id: Optional ID of the chunk (used for logging/debugging)
    """
    if not chat:
        return "OpenAI connection failed. Please check your API key."

    # Log which chunk we're processing if chunk_id is provided
    if chunk_id and st.session_state.get('debug_mode', False):
        st.write(f"Processing chunk: {chunk_id}")

    prompt = f"""
    Extract key entities and relationships from the following content as triples (subject - relation - object).
    Focus on identifying important entities and their relationships.
    Be specific and detailed in the relationships.
    Return ONLY a list of triples, one per line, in the format: subject - relation - object

    Content:
    {text[:4000]}  # Limit text to avoid token limits
    """
    try:
        response = chat([HumanMessage(content=prompt)])
        return response.content
    except Exception as e:
        st.error(f"Error extracting entities: {str(e)}")
        return ""

def parse_triples(triples_text):
    """Parse triples from various formats that might be returned by the LLM"""
    parsed_triples = []

    # Try to parse as list items (1. subject - relation - object)
    numbered_pattern = r'\d+\.\s*(.*?)\s*-\s*(.*?)\s*-\s*(.*?)(?:\n|$)'
    matches = re.findall(numbered_pattern, triples_text)
    if matches:
        return [(s.strip(), r.strip(), o.strip()) for s, r, o in matches]

    # Try to parse as plain triples (subject - relation - object)
    for line in triples_text.split('\n'):
        line = line.strip()
        if not line or line.startswith('#'):
            continue

        if ' - ' in line:
            parts = line.split(' - ')
            if len(parts) >= 3:
                subject = parts[0].strip()
                relation = parts[1].strip()
                obj = ' - '.join(parts[2:]).strip()  # Join in case object contains hyphens
                parsed_triples.append((subject, relation, obj))

    return parsed_triples

def populate_neo4j(triples_text, file_id, chunk_id=None, chunk_text=None):
    """
    Populate Neo4j with triples extracted from text.
    If chunk_id is provided, associate the triples with that chunk.
    """
    if not driver:
        st.error("Neo4j connection failed. Please check your connection settings.")
        return False

    triples = parse_triples(triples_text)
    if not triples:
        st.warning("No valid triples found in the extracted text.")
        return False

    try:
        with driver.session() as session:
            # If this is a chunk, create or update the Chunk node
            if chunk_id and chunk_text:
                # Create a Chunk node
                session.run(
                    """
                    MERGE (c:Chunk {id: $chunk_id, file_id: $file_id})
                    SET c.text = $text
                    """,
                    chunk_id=chunk_id, file_id=file_id, text=chunk_text[:1000]  # Store a preview of the text
                )

            # Process each triple
            for subject, relation, obj in triples:
                # Create the basic triple structure
                query = """
                MERGE (s:Entity {name: $subject, file_id: $file_id})
                MERGE (o:Entity {name: $object, file_id: $file_id})
                MERGE (s)-[r:RELATION {type: $relation, file_id: $file_id}]->(o)
                """

                # If this is from a chunk, connect entities to the chunk
                if chunk_id:
                    query += """
                    WITH s, o
                    MATCH (c:Chunk {id: $chunk_id})
                    MERGE (s)-[:APPEARS_IN]->(c)
                    MERGE (o)-[:APPEARS_IN]->(c)
                    """

                session.run(
                    query,
                    subject=subject, object=obj, relation=relation,
                    file_id=file_id, chunk_id=chunk_id
                )
        return True
    except Exception as e:
        st.error(f"Error populating Neo4j: {str(e)}")
        return False

def display_graph(file_id=None, keywords=None, title="Knowledge Graph", height=500):
    """
    Display a knowledge graph visualization

    Args:
        file_id: ID of the file to filter by
        keywords: Optional list of keywords to filter entities
        title: Title for the graph section
        height: Height of the graph in pixels
    """
    # Display title if provided (but not if it's already displayed as a subheader)
    if title and not any(title in s for s in ["Knowledge-Query-Graph", "Knowledge Graph"]):
        st.subheader(title)

    if not driver:
        st.error("Neo4j connection failed. Please check your connection settings.")
        return

    G = nx.DiGraph()
    try:
        with driver.session() as session:
            # Determine which file_id to use
            current_id = file_id if file_id else st.session_state.current_file_id

            # Base query - will be modified based on parameters
            if keywords:
                # Query for specific keywords
                query = """
                MATCH (a:Entity {file_id: $file_id})
                WHERE
                """
                # Add conditions for each keyword
                keyword_conditions = []
                for keyword in keywords:
                    keyword_conditions.append(f"toLower(a.name) CONTAINS toLower('{keyword}')")

                if not keyword_conditions:
                    keyword_conditions.append("TRUE")  # Fallback if no keywords

                query += " OR ".join(keyword_conditions)
                query += """
                MATCH (a)-[r:RELATION]->(b:Entity {file_id: $file_id})
                RETURN a.name AS from, r.type AS rel, b.name AS to
                UNION
                MATCH (a:Entity {file_id: $file_id})-[r:RELATION]->(b:Entity {file_id: $file_id})
                WHERE
                """
                b_conditions = []
                for keyword in keywords:
                    b_conditions.append(f"toLower(b.name) CONTAINS toLower('{keyword}')")
                query += " OR ".join(b_conditions)
                query += """
                RETURN a.name AS from, r.type AS rel, b.name AS to
                """
            else:
                # Standard query for all relationships in the document
                query = """
                MATCH (a:Entity {file_id: $file_id})-[r:RELATION]->(b:Entity {file_id: $file_id})
                RETURN a.name AS from, r.type AS rel, b.name AS to
                """

            # Execute the query
            result = session.run(query, file_id=current_id)

            # Build the graph
            for record in result:
                G.add_node(record["from"])
                G.add_node(record["to"])
                G.add_edge(record["from"], record["to"], label=record["rel"])
    except Exception as e:
        st.error(f"Error querying Neo4j: {str(e)}")
        return

    if not G.nodes():
        st.info(f"No relationships found for this {'query' if keywords else 'document'}.")
        return

    try:
        net = Network(height=f"{height}px", width="100%", directed=True, notebook=False)
        net.from_nx(G)

        # Add physics options for better visualization
        net.set_options("""
        {
          "physics": {
            "forceAtlas2Based": {
              "gravitationalConstant": -50,
              "centralGravity": 0.01,
              "springLength": 100,
              "springConstant": 0.08
            },
            "solver": "forceAtlas2Based",
            "stabilization": {
              "enabled": true,
              "iterations": 100
            }
          }
        }
        """)

        # Save and display the graph
        tmp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".html")
        net.save_graph(tmp_file.name)
        with open(tmp_file.name, "r", encoding="utf-8") as f:
            html_content = f.read()

        components.html(html_content, height=height+50)

        # Download button
        current_id = file_id if file_id else st.session_state.current_file_id
        graph_type = "query" if keywords else "full"
        st.download_button(
            label="Download Graph as HTML",
            data=html_content,
            file_name=f"{graph_type}_graph_{current_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.html",
            mime="text/html"
        )
    except Exception as e:
        st.error(f"Error creating graph visualization: {str(e)}")

def safe_cypher_query(cypher):
    """Validate and sanitize Cypher query to prevent injection and common errors"""
    # Basic validation - this is not comprehensive
    if not cypher.strip().upper().startswith("MATCH"):
        return None, "Query must start with MATCH"

    # Check for pattern expressions in RETURN clause which can cause errors
    if re.search(r'RETURN\s+.*?\(.*?\)-\[.*?\]->.*?\)', cypher, re.IGNORECASE):
        # Fix by modifying the query to use proper variable binding
        return None, "Pattern expressions in RETURN clause are not allowed"

    # Add file_id filter if not present
    if "file_id" not in cypher and st.session_state.current_file_id:
        # Attempt to add a WHERE clause after MATCH pattern properly
        # Find the first MATCH clause and add WHERE condition after the pattern
        # This is a simple heuristic and may not cover all cases
        pattern = re.compile(r'(MATCH\s*\([^\)]*\))', re.IGNORECASE)
        match = pattern.search(cypher)
        if match:
            match_text = match.group(1)
            # Check if WHERE already exists after this MATCH
            after_match = cypher[match.end():]
            if re.search(r'\bWHERE\b', after_match, re.IGNORECASE):
                # Add AND condition to existing WHERE
                cypher = re.sub(r'(MATCH\s*\([^\)]*\)\s*WHERE\s*)',
                                f"\\1n.file_id = '{st.session_state.current_file_id}' AND ",
                                cypher, flags=re.IGNORECASE)
            else:
                # Insert WHERE clause after MATCH pattern
                cypher = cypher.replace(match_text, f"{match_text} WHERE n.file_id = '{st.session_state.current_file_id}'")
        else:
            # If no MATCH found, leave unchanged
            pass

    return cypher, None

def extract_keywords_from_question(question):
    """Extract key entities and concepts from the question"""
    if not chat:
        return []

    prompt = f"""
    Extract the key entities, concepts, and search terms from this question.
    Return ONLY a comma-separated list of important terms (nouns, proper nouns, key concepts).

    Question: {question}
    """

    try:
        response = chat([HumanMessage(content=prompt)])
        keywords = [kw.strip() for kw in response.content.split(',')]
        return [kw for kw in keywords if kw]  # Filter out empty strings
    except Exception as e:
        st.error(f"Error extracting keywords: {str(e)}")
        # Fallback to simple word extraction
        return [w for w in re.findall(r'\b\w+\b', question) if len(w) > 3]

def query_neo4j_for_question(question):
    """Generate and execute Cypher queries based on the question"""
    if not chat:
        return "MATCH (n) RETURN n LIMIT 5", "OpenAI connection failed"

    # Extract keywords from the question
    keywords = extract_keywords_from_question(question)

    cypher_prompt = f"""
    Given the user question: "{question}", write a Cypher query that could help answer it using a graph of entities and relationships.

    The graph schema is:
    - Nodes have label 'Entity' with properties 'name' and 'file_id'
    - Relationships have type 'RELATION' with property 'type' containing the relation name
    - Nodes are connected to Chunk nodes via APPEARS_IN relationships
    - Chunk nodes have 'id', 'file_id', and 'text' properties

    Important keywords from the question: {', '.join(keywords)}

    Your query should:
    1. Find entities that match or are related to these keywords
    2. Explore relationships between these entities
    3. Return the relevant entities, relationships, and the text chunks they appear in
    4. Filter by file_id = '{st.session_state.current_file_id}'

    Return ONLY the Cypher query without any explanation or markdown formatting.
    """

    try:
        cypher_response = chat([HumanMessage(content=cypher_prompt)]).content
        # Extract the query if it's wrapped in backticks
        if "```" in cypher_response:
            cypher_response = re.search(r'```(?:cypher)?(.*?)```', cypher_response, re.DOTALL).group(1).strip()

        cypher, error = safe_cypher_query(cypher_response)
        if error:
            # Fallback query that searches for entities matching keywords
            fallback_query = f"""
            MATCH (e\:Entity)
            WHERE e.file_id = '{st.session_state.current_file_id}'
            AND (
            """

            # Add conditions for each keyword
            keyword_conditions = []
            for keyword in keywords:
                keyword_conditions.append(f"toLower(e.name) CONTAINS toLower('{keyword}')")

            if not keyword_conditions:
                keyword_conditions.append("TRUE")  # Fallback if no keywords

            fallback_query += " OR ".join(keyword_conditions)
            fallback_query += """
            )
            OPTIONAL MATCH (e)-[r\:RELATION]-(related\:Entity)
            WHERE related.file_id = e.file_id
            OPTIONAL MATCH (e)-[\:APPEARS_IN]->(c\:Chunk)
            RETURN e.name AS entity,
                   type(r) AS relation,
                   related.name AS related_entity,
                   c.text AS context
            LIMIT 20
            """

            return fallback_query, error

        return cypher, None
    except Exception as e:
        # Return a simple fallback query
        return f"""
        MATCH (e\:Entity)
        WHERE e.file_id = '{st.session_state.current_file_id}'
        OPTIONAL MATCH (e)-[\:APPEARS_IN]->(c\:Chunk)
        RETURN e.name AS entity, c.text AS context
        LIMIT 10
        """, str(e)

def determine_query_type(question):
    """
    Determine the type of query (direct, indirect, short, long, multiple questions, etc.)
    """
    # Simple heuristic to determine query type
    if " and " in question.lower() or " or " in question.lower():
        return "multiple_questions"
    elif len(question.split()) > 20:
        return "long"
    elif len(question.split()) <= 5:
        return "short"
    elif "draw" in question.lower() or "outline" in question.lower():
        return "structural"
    elif "image" in question.lower() or "table" in question.lower():
        return "visual"
    else:
        return "direct"

def select_best_model(query_type):
    """
    Select the best model based on the query type
    """
    model_mapping = {
        "direct": "gpt-3.5-turbo",
        "indirect": "gpt-3.5-turbo",
        "short": "gpt-3.5-turbo",
        "long": "gpt-4",
        "multiple_questions": "gpt-4",
        "structural": "gpt-4",
        "visual": "gpt-4"
    }
    return model_mapping.get(query_type, "gpt-3.5-turbo")

def create_vector_store(file_id):
    """Create a vector store for a file if it doesn't exist yet"""
    if file_id in st.session_state.vector_stores:
        return st.session_state.vector_stores[file_id]

    # Get chunks for this file
    chunk_texts = st.session_state.chunks.get(file_id, [])
    if not chunk_texts:
        st.warning(f"No document chunks found for file {file_id}")
        return None

    # Create document objects with metadata
    docs = [Document(page_content=chunk, metadata={"file_id": file_id, "chunk_idx": i})
            for i, chunk in enumerate(chunk_texts)]

    # Create vector store
    try:
        vectorstore = FAISS.from_documents(docs, embeddings)
        st.session_state.vector_stores[file_id] = vectorstore
        return vectorstore
    except Exception as e:
        st.error(f"Error creating vector store: {str(e)}")
        return None

def run_hybrid_rag(question, file_id):
    """
    Run a hybrid RAG process that:
    1. Extracts keywords from the question
    2. Retrieves relevant text chunks using vector similarity search
    3. Generates an answer based on the retrieved chunks
    """
    if not chat:
        st.error("Error: OpenAI connection failed. Please check your API key.")
        return "Error: OpenAI connection failed. Please check your API key.", ""

    # Set the current file_id to ensure queries target the right document
    st.session_state.current_file_id = file_id

    # Extract keywords for better context
    keywords = extract_keywords_from_question(question)
    st.session_state.last_keywords = keywords  # Store for display

    # Create or get vector store for this file
    vectorstore = create_vector_store(file_id)
    if not vectorstore:
        return "Failed to create vector search index for this document.", ""

    # Determine the type of query and select appropriate model
    query_type = determine_query_type(question)
    model_name = select_best_model(query_type)
    llm = ChatOpenAI(openai_api_key=OPENAI_API_KEY, model_name=model_name)

    try:
        # Create a retriever
        retriever = vectorstore.as_retriever(search_kwargs={"k": 5})
        # Create a ConversationalRetrievalChain
        qa = ConversationalRetrievalChain.from_llm(
            llm=llm,
            retriever=retriever,
            return_source_documents=True,
            condense_question_prompt="""
            Given the following conversation and a follow-up question, rephrase the follow-up question to be a standalone question.

            Chat History:
            {chat_history}

            Follow-Up Question: {question}

            Standalone Question:
            """
        )

        # Generate a Neo4j query to supplement the vector search results
        cypher_query, error = query_neo4j_for_question(question)

        # Create a comprehensive prompt that includes the keywords
        prompt = f"""
        Answer the following question based on the retrieved document chunks:

        Question: {question}

        Focus on the key concepts: {', '.join(keywords)}

        If the retrieved information isn't sufficient to answer the question fully,
        please state this clearly. Be specific about what information seems to be missing.

        Provide a concise but informative answer.
        """

        # Execute the QA chain
        result = qa({"question": question, "chat_history": st.session_state.conversation_history})
        answer = result["answer"]

        # Update conversation history
        st.session_state.conversation_history.append((question, answer))

        return answer, cypher_query
    except Exception as e:
        st.error(f"Error in RAG process: {str(e)}")
        return f"Error generating answer: {str(e)}", ""

# File upload and processing
col1, col2 = st.columns([2, 5])

with col1:
    st.subheader("Upload Documents")

    # Chunking settings
    with st.expander("Advanced Settings"):
        chunk_size = st.slider("Chunk Size (characters)", 500, 5000, 2000, 500)
        chunk_overlap = st.slider("Chunk Overlap (characters)", 50, 500, 200, 50)

        # Initialize debug_mode in session state if it doesn't exist
        if "debug_mode" not in st.session_state:
            st.session_state.debug_mode = False

        # Debug mode toggle
        debug_mode = st.checkbox("Debug Mode", value=st.session_state.debug_mode)
        st.session_state.debug_mode = debug_mode

        if debug_mode:
            st.info("Debug mode enabled. Additional information will be shown during processing.")

    uploaded_files = st.file_uploader("Upload PDFs, Word Docs, or Images",
                                     type=["pdf", "docx", "png", "jpg", "jpeg"],
                                     accept_multiple_files=True)

    if uploaded_files:
        with st.spinner("Processing documents..."):
            for file in uploaded_files:
                file_type = file.name.split(".")[-1].lower()

                st.info(f"Processing {file.name}...")
                text = ""

                # Extract text based on file type
                if file_type == "pdf":
                    text = extract_text_from_pdf(file)
                elif file_type == "docx":
                    text = extract_text_from_docx(file)
                elif file_type in ["png", "jpg", "jpeg"]:
                    text = extract_text_from_image(file)

                if not text:
                    st.error(f"Could not extract text from {file.name}")
                    continue

                # Collect metadata about the document
                try:
                    file_size = len(file.getvalue())
                    file_stats = {
                        "name": file.name,
                        "type": file_type,
                        "size": file_size,
                        "content_length": len(text),
                        "content_preview": text[:100]
                    }

                    # For PDFs, try to extract more metadata
                    if file_type == "pdf":
                        reader = PdfReader(file)
                        file_stats["pages"] = len(reader.pages)
                        if reader.metadata:
                            for key in reader.metadata:
                                if key and reader.metadata[key]:
                                    file_stats[f"pdf_{key}"] = str(reader.metadata[key])
                except Exception as e:
                    st.warning(f"Could not extract complete metadata: {str(e)}")

                # Check if this document is a duplicate
                is_duplicate, existing_id = is_duplicate_document(text, file_stats)

                if is_duplicate:
                    st.warning(f"This document appears to be identical to a previously processed document.")

                    # Ask user if they want to use the existing document
                    if st.button(f"Use existing document '{st.session_state.file_names.get(existing_id, 'Unknown')}'"):
                        st.session_state.current_file_id = existing_id
                        st.success(f"Switched to existing document.")
                        st.experimental_rerun()
                    continue

                # Generate a new file ID
                file_id = str(uuid4())[:8]

                # Store document in session state
                st.session_state.file_ids.append(file_id)
                st.session_state.file_names[file_id] = file.name
                st.session_state.file_metadata[file_id] = file_stats

                # Calculate and store document hash
                doc_hash = calculate_document_hash(text, file_stats)
                st.session_state.file_hashes[doc_hash] = file_id

                # Process the document
                with st.spinner(f"Chunking text and extracting knowledge graph..."):
                    # Split the document into chunks
                    chunks = chunk_text(text, chunk_size=chunk_size, overlap=chunk_overlap)
                    st.session_state.chunks[file_id] = chunks

                    if debug_mode:
                        st.write(f"Created {len(chunks)} chunks")

                    # Process each chunk to extract entities and relationships
                    for i, chunk in enumerate(chunks):
                        # Generate a unique ID for this chunk
                        chunk_id = generate_chunk_id(chunk, file_id, i)

                        # Store metadata about this chunk
                        if file_id not in st.session_state.chunk_metadata:
                            st.session_state.chunk_metadata[file_id] = {}

                        st.session_state.chunk_metadata[file_id][chunk_id] = {
                            "index": i,
                            "length": len(chunk),
                            "preview": chunk[:100]
                        }

                        # Extract entities and relationships using OpenAI
                        if debug_mode:
                            st.write(f"Processing chunk {i+1}/{len(chunks)}")

                        triples_text = extract_entities_with_openai(chunk, chunk_id)

                        # Populate Neo4j with extracted knowledge
                        populate_neo4j(triples_text, file_id, chunk_id, chunk)

                # Set the current file to the newly processed file
                st.session_state.current_file_id = file_id

                # Create a vector store for the document
                create_vector_store(file_id)

                st.success(f"Successfully processed {file.name}")
                st.rerun()

with col2:
    st.subheader("Knowledge Graph Explorer")

    # Document selector
    if st.session_state.file_ids:
        # Create a dropdown to select which document to view
        file_options = {st.session_state.file_names[fid]: fid for fid in st.session_state.file_ids}
        selected_doc_name = st.selectbox(
            "Select Document",
            options=list(file_options.keys()),
            index=0 if st.session_state.current_file_id is None else
                  list(file_options.keys()).index(st.session_state.file_names[st.session_state.current_file_id])
                  if st.session_state.current_file_id in st.session_state.file_ids else 0
        )
        selected_doc_id = file_options[selected_doc_name]

        # Update current file ID if changed
        if selected_doc_id != st.session_state.current_file_id:
            st.session_state.current_file_id = selected_doc_id
            st.experimental_rerun()

        # Display file metadata
        if selected_doc_id in st.session_state.file_metadata:
            metadata = st.session_state.file_metadata[selected_doc_id]
            with st.expander("Document Details"):
                for key, value in metadata.items():
                    if key != "content_preview":  # Skip the preview
                        st.write(f"**{key}:** {value}")

        # Display document graph
        col3, col4 = st.columns(2)
        with col3:
            st.subheader("Knowledge Graph")
            display_graph(file_id=selected_doc_id, title="Knowledge Graph", height=600)

        with col4:
            st.subheader("Cypher Query Explorer")
            cypher_query = st.text_area(
                "Enter Cypher Query",
                value=f"""
                MATCH (e\:Entity {{file_id: '{selected_doc_id}'}})
                RETURN e.name AS Entity
                LIMIT 10
                """
            )

            if st.button("Run Query"):
                if driver:
                    try:
                        with driver.session() as session:
                            result = session.run(cypher_query)
                            # Convert to dataframe for display
                            import pandas as pd
                            records = [record.data() for record in result]
                            if records:
                                df = pd.DataFrame(records)
                                st.dataframe(df)
                            else:
                                st.info("Query returned no results")
                    except Exception as e:
                        st.error(f"Error executing query: {str(e)}")
                else:
                    st.error("Neo4j connection failed")

        # Query interface
        st.subheader("Ask Questions About Your Document")
        query = st.text_input("Enter your question:")
        if st.button("Get Answer"):
            with st.spinner("Generating answer..."):
                answer, generated_cypher = run_hybrid_rag(query, selected_doc_id)

                # Store in history
                st.session_state.history.append({
                    "question": query,
                    "answer": answer,
                    "file_id": selected_doc_id,
                    "cypher": generated_cypher,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })

                # Display the answer
                st.markdown("### Answer:")
                st.markdown(answer)

                # Show the generated Cypher query
                with st.expander("Show Generated Cypher Query"):
                    st.code(generated_cypher, language="cypher")

                # Display a mini-graph related to the question
                if hasattr(st.session_state, 'last_keywords') and st.session_state.last_keywords:
                    st.subheader("Knowledge-Query-Graph")
                    display_graph(file_id=selected_doc_id, keywords=st.session_state.last_keywords, height=400)
    else:
        st.info("Upload a document to start exploring")

# Display chat history in sidebar
if st.session_state.history:
    for i, item in enumerate(reversed(st.session_state.history)):
        with st.sidebar.expander(f"Q: {item['question'][:50]}..."):
            st.write(f"**File:** {st.session_state.file_names.get(item['file_id'], 'Unknown')}")
            st.write(f"**Time:** {item['timestamp']}")
            st.markdown(f"**Q:** {item['question']}")
            st.markdown(f"**A:** {item['answer']}")

            # Option to view generated Cypher
            if 'cypher' in item and item['cypher']:
                st.code(item['cypher'], language="cypher")
else:
    st.sidebar.info("No questions asked yet. Your conversation history will appear here.")

# Add option to clear history
if st.session_state.history and st.sidebar.button("Clear History"):
    st.session_state.history = []
    st.session_state.conversation_history = []
    st.experimental_rerun()

# Add option to remove all documents
if st.session_state.file_ids and st.sidebar.button("Remove All Documents", type="primary"):
    # Clear session state
    st.session_state.file_ids = []
    st.session_state.file_names = {}
    st.session_state.file_hashes = {}
    st.session_state.file_metadata = {}
    st.session_state.current_file_id = None
    st.session_state.chunks = {}
    st.session_state.chunk_metadata = {}
    st.session_state.vector_stores = {}

    # Clear Neo4j database
    if driver:
        try:
            with driver.session() as session:
                session.run("MATCH (n) DETACH DELETE n")
            st.sidebar.success("All documents removed and database cleared")
        except Exception as e:
            st.sidebar.error(f"Error clearing database: {str(e)}")

    st.experimental_rerun()

# Display footer with helpful information
st.markdown("---")
st.markdown("""
    **How to use this app:**
    1. Upload documents (PDF, DOCX, or images)
    2. Explore the knowledge graph extracted from your documents
    3. Ask questions to get AI-powered insights from your documents

    This app combines knowledge graph extraction with vector search for more comprehensive document understanding.
""")

# Close Neo4j driver on app shutdown
if driver:
    driver.close()
